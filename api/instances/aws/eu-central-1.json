{"16Cores": {"onDemand": [{"Architecture": ["x86_64"], "BestOnDemandHourPriceRegion": "ap-south-1", "CanonicalName": "c5a.4xlarge", "InstanceType": "c5a.4xlarge", "Memory": 32768, "ModifiedDate": "2024-11-13 00:29:56", "PaymentType": "OnDemand", "PricePerHour": 0.696, "tags": ["cheapest"]}, {"Architecture": ["x86_64"], "BestOnDemandHourPriceRegion": "ap-south-1", "CanonicalName": "c6a.4xlarge", "InstanceType": "c6a.4xlarge", "Memory": 32768, "ModifiedDate": "2024-11-13 00:29:56", "PaymentType": "OnDemand", "PricePerHour": 0.6984, "tags": []}, {"Architecture": ["x86_64"], "BestOnDemandHourPriceRegion": "ap-south-1", "CanonicalName": "c7i-flex.4xlarge", "InstanceType": "c7i-flex.4xlarge", "Memory": 32768, "ModifiedDate": "2024-11-13 00:29:56", "PaymentType": "OnDemand", "PricePerHour": 0.77406, "tags": []}, {"Architecture": ["x86_64"], "BestOnDemandHourPriceRegion": "ap-south-1", "CanonicalName": "c5.4xlarge", "InstanceType": "c5.4xlarge", "Memory": 32768, "ModifiedDate": "2024-11-13 00:29:56", "PaymentType": "OnDemand", "PricePerHour": 0.776, "tags": []}], "spot": [{"Architecture": ["x86_64"], "BestOnDemandHourPriceRegion": "ap-south-1", "CanonicalName": "c7i-flex.4xlarge", "InstanceType": "c7i-flex.4xlarge", "Memory": 32768, "ModifiedDate": "2024-11-13 00:29:56", "PaymentType": "OnDemand", "PricePerHour": 0.1347121212121212, "tags": ["cheapest"]}, {"Architecture": ["x86_64"], "BestOnDemandHourPriceRegion": "ap-south-1", "CanonicalName": "c5.4xlarge", "InstanceType": "c5.4xlarge", "Memory": 32768, "ModifiedDate": "2024-11-13 00:29:56", "PaymentType": "OnDemand", "PricePerHour": 0.19669545454545456, "tags": []}, {"Architecture": ["x86_64"], "BestOnDemandHourPriceRegion": "ap-south-1", "CanonicalName": "c6a.4xlarge", "InstanceType": "c6a.4xlarge", "Memory": 32768, "ModifiedDate": "2024-11-13 00:29:56", "PaymentType": "OnDemand", "PricePerHour": 0.24041627906976742, "tags": []}, {"Architecture": ["x86_64"], "BestOnDemandHourPriceRegion": "ap-southeast-5", "CanonicalName": "c6i.4xlarge", "InstanceType": "c6i.4xlarge", "Memory": 32768, "ModifiedDate": "2024-11-13 00:29:56", "PaymentType": "OnDemand", "PricePerHour": 0.2559021739130435, "tags": []}]}, "4Cores": {"onDemand": [{"Architecture": ["x86_64"], "BestOnDemandHourPriceRegion": "ap-south-1", "CanonicalName": "t3a.xlarge", "InstanceType": "t3a.xlarge", "Memory": 16384, "ModifiedDate": "2024-11-13 00:29:56", "PaymentType": "OnDemand", "PricePerHour": 0.1728, "tags": ["cheapest"]}, {"Architecture": ["x86_64"], "BestOnDemandHourPriceRegion": "ap-south-1", "CanonicalName": "c5a.xlarge", "InstanceType": "c5a.xlarge", "Memory": 8192, "ModifiedDate": "2024-11-13 00:29:56", "PaymentType": "OnDemand", "PricePerHour": 0.174, "tags": []}, {"Architecture": ["x86_64"], "BestOnDemandHourPriceRegion": "ap-south-1", "CanonicalName": "c6a.xlarge", "InstanceType": "c6a.xlarge", "Memory": 8192, "ModifiedDate": "2024-11-13 00:29:56", "PaymentType": "OnDemand", "PricePerHour": 0.1746, "tags": []}, {"Architecture": ["x86_64"], "BestOnDemandHourPriceRegion": "us-east-1", "CanonicalName": "t3.xlarge", "InstanceType": "t3.xlarge", "Memory": 16384, "ModifiedDate": "2024-11-13 00:29:56", "PaymentType": "OnDemand", "PricePerHour": 0.192, "tags": []}], "spot": [{"Architecture": ["x86_64"], "BestOnDemandHourPriceRegion": "ap-south-1", "CanonicalName": "c7i-flex.xlarge", "InstanceType": "c7i-flex.xlarge", "Memory": 8192, "ModifiedDate": "2024-11-13 00:29:56", "PaymentType": "OnDemand", "PricePerHour": 0.03441034482758621, "tags": ["cheapest"]}, {"Architecture": ["x86_64"], "BestOnDemandHourPriceRegion": "us-east-1", "CanonicalName": "m5.xlarge", "InstanceType": "m5.xlarge", "Memory": 16384, "ModifiedDate": "2024-11-13 00:29:56", "PaymentType": "OnDemand", "PricePerHour": 0.06491666666666666, "tags": []}, {"Architecture": ["x86_64"], "BestOnDemandHourPriceRegion": "ap-south-1", "CanonicalName": "c5a.xlarge", "InstanceType": "c5a.xlarge", "Memory": 8192, "ModifiedDate": "2024-11-13 00:29:56", "PaymentType": "OnDemand", "PricePerHour": 0.06762926829268293, "tags": []}, {"Architecture": ["x86_64"], "BestOnDemandHourPriceRegion": "ap-south-1", "CanonicalName": "t3a.xlarge", "InstanceType": "t3a.xlarge", "Memory": 16384, "ModifiedDate": "2024-11-13 00:29:56", "PaymentType": "OnDemand", "PricePerHour": 0.0694081081081081, "tags": []}]}, "8Cores": {"onDemand": [{"Architecture": ["x86_64"], "BestOnDemandHourPriceRegion": "ap-south-1", "CanonicalName": "t3a.2xlarge", "InstanceType": "t3a.2xlarge", "Memory": 32768, "ModifiedDate": "2024-11-13 00:29:56", "PaymentType": "OnDemand", "PricePerHour": 0.3456, "tags": ["cheapest"]}, {"Architecture": ["x86_64"], "BestOnDemandHourPriceRegion": "ap-south-1", "CanonicalName": "c5a.2xlarge", "InstanceType": "c5a.2xlarge", "Memory": 16384, "ModifiedDate": "2024-11-13 00:29:56", "PaymentType": "OnDemand", "PricePerHour": 0.348, "tags": []}, {"Architecture": ["x86_64"], "BestOnDemandHourPriceRegion": "ap-south-1", "CanonicalName": "c6a.2xlarge", "InstanceType": "c6a.2xlarge", "Memory": 16384, "ModifiedDate": "2024-11-13 00:29:56", "PaymentType": "OnDemand", "PricePerHour": 0.3492, "tags": []}, {"Architecture": ["x86_64"], "BestOnDemandHourPriceRegion": "us-east-1", "CanonicalName": "t3.2xlarge", "InstanceType": "t3.2xlarge", "Memory": 32768, "ModifiedDate": "2024-11-13 00:29:56", "PaymentType": "OnDemand", "PricePerHour": 0.384, "tags": []}], "spot": [{"Architecture": ["x86_64"], "BestOnDemandHourPriceRegion": "ap-south-1", "CanonicalName": "c7i-flex.2xlarge", "InstanceType": "c7i-flex.2xlarge", "Memory": 16384, "ModifiedDate": "2024-11-13 00:29:56", "PaymentType": "OnDemand", "PricePerHour": 0.062338235294117646, "tags": ["cheapest"]}, {"Architecture": ["x86_64"], "BestOnDemandHourPriceRegion": "ap-south-1", "CanonicalName": "c6a.2xlarge", "InstanceType": "c6a.2xlarge", "Memory": 16384, "ModifiedDate": "2024-11-13 00:29:56", "PaymentType": "OnDemand", "PricePerHour": 0.1256609756097561, "tags": []}, {"Architecture": ["x86_64"], "BestOnDemandHourPriceRegion": "ap-south-1", "CanonicalName": "c5a.2xlarge", "InstanceType": "c5a.2xlarge", "Memory": 16384, "ModifiedDate": "2024-11-13 00:29:56", "PaymentType": "OnDemand", "PricePerHour": 0.12830666666666665, "tags": []}, {"Architecture": ["x86_64"], "BestOnDemandHourPriceRegion": "ap-south-1", "CanonicalName": "c5.2xlarge", "InstanceType": "c5.2xlarge", "Memory": 16384, "ModifiedDate": "2024-11-13 00:29:56", "PaymentType": "OnDemand", "PricePerHour": 0.1321263157894737, "tags": []}]}}