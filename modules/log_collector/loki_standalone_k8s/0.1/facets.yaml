intent: log_collector
flavor: loki_standalone_k8s
version: "0.1"
description: Adds log collector module
clouds:
  - aws
  - azure
  - gcp
  - kubernetes
inputs:
  kubernetes_details:
    type: "@output/kubernetes"
    displayName: Kubernetes Cluster
    description: The details of kubernetes cluster where the loki standalone will be installed
    optional: false
    default:
      resource_type: kubernetes_cluster
      resource_name: default
spec:
  title: Loki Configuration
  type: object
  properties:
    loki:
      title: Loki
      type: object
      required: ["replicas", "volume"]
      properties:
        replicas:
          type: integer
          title: Replicas
          description: Number of Loki replicas
          minimum: 1
          maximum: 10
          x-ui-placeholder: "Enter replicas (e.g., 2)"
          x-ui-error-message: "Replicas must be between 1 and 10."
        volume:
          type: string
          title: Volume
          description: Storage volume for Loki
          pattern: "^[0-9]+(\\.[0-9]+)?Gi$"
          x-ui-placeholder: "e.g., '5Gi' or '10Gi'"
          x-ui-error-message: "Volume must be in the format 'XGi' (e.g., '5Gi')."
        loki_standalone:
          title: Loki Standalone
          type: object
          required: ["timeout", "wait", "recreate_pods", "version"]
          properties:
            timeout:
              type: integer
              title: Timeout
              description: "Timeout for deployment."
              minimum: 300
              maximum: 1800
              x-ui-placeholder: "Enter timeout in seconds (e.g., 700)"
              x-ui-error-message: "Timeout must be between 300s (5m) and 1800s (30m)."
            wait:
              type: boolean
              title: Wait
              description: "Wait for deployment completion."
            recreate_pods:
              type: boolean
              title: Recreate Pods
              description: "Recreate pods during updates."
            version:
              type: string
              title: Version for loki helm chart
              description: "Loki chart version"
            values:
              type: object
              title: Custom Values
              description: Overrides for Loki configuration
              x-ui-yaml-editor: true
              x-ui-placeholder: |
                # Example:
                # loki:
                #   singleBinary:
                #     resources:
                #       requests:
                #         memory: "200Mi"
      x-ui-order: ["replicas", "volume", "loki_standalone"]
    minio:
      title: Minio
      type: object
      properties:
        replicas:
          type: integer
          title: Replicas
          description: Number of Minio replicas
          minimum: 1
          x-ui-placeholder: "Enter replicas (e.g., 2)"
          x-ui-error-message: "Replicas must be between 1 and 10."
        volume:
          type: string
          title: Volume
          description: Storage volume for Minio
          pattern: "^[0-9]+(\\.[0-9]+)?Gi$"
          x-ui-placeholder: "e.g., '10Gi' or '20Gi'"
          x-ui-error-message: "Volume must be in the format 'XGi' (e.g., '10Gi')."
        auth:
          title: Authentication
          type: object
          properties:
            rootUser:
              type: string
              title: Root User
              description: Username for Minio root access
              x-ui-placeholder: "Enter root user (e.g., 'facets-user')"
            rootPassword:
              type: string
              title: Root Password
              description: Password for Minio root access
              x-ui-placeholder: "Enter root password"
        values:
          type: object
          title: Custom Values
          description: Overrides for Minio configuration
          x-ui-yaml-editor: true
          x-ui-placeholder: |
            # Example:
            # minio:
            #   resources:
            #     requests:
            #       memory: "300Mi"
      x-ui-order: ["replicas", "volume", "auth", "values"]
    promtail:
      title: Promtail
      type: object
      properties:
        timeout:
          type: integer
          title: Timeout
          description: "Timeout for deployment (default: 600)"
          minimum: 300
          maximum: 1800
          x-ui-placeholder: "Enter timeout in seconds."
          x-ui-error-message: "Timeout must be between 300s (5m) and 1800s (30m)."
        wait:
          type: boolean
          title: Wait
          description: "Wait for deployment completion."
        recreate_pods:
          type: boolean
          title: Recreate Pods
          description: "Recreate pods during updates."
        version:
          type: string
          title: Version
          description: "Promtail chart version (default: 6.7.4)"
          x-ui-placeholder: "Select a version"
        values:
          type: object
          title: Custom Values
          description: Overrides for Promtail configuration
          x-ui-yaml-editor: true
          x-ui-placeholder: |
            # Example:
            # promtail:
            #   config:
            #     clients:
            #       - url: http://loki:3100/loki/api/v1/push
      x-ui-order: ["timeout", "wait", "recreate_pods", "version", "values"]
  required: ["loki"]
sample:
  $schema: "https://facets-cloud.github.io/facets-schemas/schemas/log_collector/log_collector.schema.json"
  kind: log_collector
  version: "0.1"
  flavor: loki_standalone_k8s
  disabled: true
  metadata: {}
  spec:
    loki:
      replicas: 2
      volume: 5Gi
      loki_standalone:
        timeout: 700
        wait: true
        values: {}
    minio:
      replicas: 2
      volume: 10Gi
      auth: {}
      values: {}
    promtail:
      timeout: 650
      wait: true
      values: {}
  advanced:
    loki: {}
