{"16Cores": {"onDemand": [{"Architecture": ["x86_64"], "BestOnDemandHourPriceRegion": "ap-south-1", "CanonicalName": "c6a.4xlarge", "InstanceType": "c6a.4xlarge", "Memory": 32768, "ModifiedDate": "2024-11-13 00:29:56", "PaymentType": "OnDemand", "PricePerHour": 0.9432, "tags": ["cheapest"]}, {"Architecture": ["x86_64"], "BestOnDemandHourPriceRegion": "ap-south-1", "CanonicalName": "c5a.4xlarge", "InstanceType": "c5a.4xlarge", "Memory": 32768, "ModifiedDate": "2024-11-13 00:29:56", "PaymentType": "OnDemand", "PricePerHour": 0.944, "tags": []}, {"Architecture": ["x86_64"], "BestOnDemandHourPriceRegion": "ap-south-1", "CanonicalName": "c7i-flex.4xlarge", "InstanceType": "c7i-flex.4xlarge", "Memory": 32768, "ModifiedDate": "2024-11-13 00:29:56", "PaymentType": "OnDemand", "PricePerHour": 1.04538, "tags": []}, {"Architecture": ["x86_64"], "BestOnDemandHourPriceRegion": "ap-south-1", "CanonicalName": "c5.4xlarge", "InstanceType": "c5.4xlarge", "Memory": 32768, "ModifiedDate": "2024-11-13 00:29:56", "PaymentType": "OnDemand", "PricePerHour": 1.048, "tags": []}], "spot": [{"Architecture": ["x86_64"], "BestOnDemandHourPriceRegion": "ap-south-1", "CanonicalName": "m6a.4xlarge", "InstanceType": "m6a.4xlarge", "Memory": 65536, "ModifiedDate": "2024-11-13 00:29:56", "PaymentType": "OnDemand", "PricePerHour": 0.117022, "tags": ["cheapest"]}, {"Architecture": ["x86_64"], "BestOnDemandHourPriceRegion": "us-east-1", "CanonicalName": "c4.4xlarge", "InstanceType": "c4.4xlarge", "Memory": 30720, "ModifiedDate": "2024-11-13 00:29:56", "PaymentType": "OnDemand", "PricePerHour": 0.12449615384615384, "tags": []}, {"Architecture": ["x86_64"], "BestOnDemandHourPriceRegion": "ap-southeast-5", "CanonicalName": "c6id.4xlarge", "InstanceType": "c6id.4xlarge", "Memory": 32768, "ModifiedDate": "2024-11-13 00:29:56", "PaymentType": "OnDemand", "PricePerHour": 0.1538030303030303, "tags": []}, {"Architecture": ["x86_64"], "BestOnDemandHourPriceRegion": "us-east-1", "CanonicalName": "m4.4xlarge", "InstanceType": "m4.4xlarge", "Memory": 65536, "ModifiedDate": "2024-11-13 00:29:56", "PaymentType": "OnDemand", "PricePerHour": 0.15509714285714285, "tags": []}]}, "4Cores": {"onDemand": [{"Architecture": ["x86_64"], "BestOnDemandHourPriceRegion": "ap-south-1", "CanonicalName": "c6a.xlarge", "InstanceType": "c6a.xlarge", "Memory": 8192, "ModifiedDate": "2024-11-13 00:29:56", "PaymentType": "OnDemand", "PricePerHour": 0.2358, "tags": ["cheapest"]}, {"Architecture": ["x86_64"], "BestOnDemandHourPriceRegion": "ap-south-1", "CanonicalName": "c5a.xlarge", "InstanceType": "c5a.xlarge", "Memory": 8192, "ModifiedDate": "2024-11-13 00:29:56", "PaymentType": "OnDemand", "PricePerHour": 0.236, "tags": []}, {"Architecture": ["x86_64"], "BestOnDemandHourPriceRegion": "ap-south-1", "CanonicalName": "t3a.xlarge", "InstanceType": "t3a.xlarge", "Memory": 16384, "ModifiedDate": "2024-11-13 00:29:56", "PaymentType": "OnDemand", "PricePerHour": 0.2419, "tags": []}, {"Architecture": ["x86_64"], "BestOnDemandHourPriceRegion": "ap-south-1", "CanonicalName": "c7i-flex.xlarge", "InstanceType": "c7i-flex.xlarge", "Memory": 8192, "ModifiedDate": "2024-11-13 00:29:56", "PaymentType": "OnDemand", "PricePerHour": 0.26135, "tags": []}], "spot": [{"Architecture": ["x86_64"], "BestOnDemandHourPriceRegion": "ap-southeast-5", "CanonicalName": "c6id.xlarge", "InstanceType": "c6id.xlarge", "Memory": 8192, "ModifiedDate": "2024-11-13 00:29:56", "PaymentType": "OnDemand", "PricePerHour": 0.03368076923076923, "tags": ["cheapest"]}, {"Architecture": ["x86_64"], "BestOnDemandHourPriceRegion": "us-east-1", "CanonicalName": "inf1.xlarge", "InstanceType": "inf1.xlarge", "Memory": 8192, "ModifiedDate": "2024-11-13 00:29:56", "PaymentType": "OnDemand", "PricePerHour": 0.04024166666666667, "tags": []}, {"Architecture": ["x86_64"], "BestOnDemandHourPriceRegion": "ap-south-1", "CanonicalName": "c7i-flex.xlarge", "InstanceType": "c7i-flex.xlarge", "Memory": 8192, "ModifiedDate": "2024-11-13 00:29:56", "PaymentType": "OnDemand", "PricePerHour": 0.05886190476190476, "tags": []}, {"Architecture": ["x86_64"], "BestOnDemandHourPriceRegion": "us-east-1", "CanonicalName": "m5d.xlarge", "InstanceType": "m5d.xlarge", "Memory": 16384, "ModifiedDate": "2024-11-13 00:29:56", "PaymentType": "OnDemand", "PricePerHour": 0.061112, "tags": []}]}, "8Cores": {"onDemand": [{"Architecture": ["x86_64"], "BestOnDemandHourPriceRegion": "ap-south-1", "CanonicalName": "c6a.2xlarge", "InstanceType": "c6a.2xlarge", "Memory": 16384, "ModifiedDate": "2024-11-13 00:29:56", "PaymentType": "OnDemand", "PricePerHour": 0.4716, "tags": ["cheapest"]}, {"Architecture": ["x86_64"], "BestOnDemandHourPriceRegion": "ap-south-1", "CanonicalName": "c5a.2xlarge", "InstanceType": "c5a.2xlarge", "Memory": 16384, "ModifiedDate": "2024-11-13 00:29:56", "PaymentType": "OnDemand", "PricePerHour": 0.472, "tags": []}, {"Architecture": ["x86_64"], "BestOnDemandHourPriceRegion": "ap-south-1", "CanonicalName": "t3a.2xlarge", "InstanceType": "t3a.2xlarge", "Memory": 32768, "ModifiedDate": "2024-11-13 00:29:56", "PaymentType": "OnDemand", "PricePerHour": 0.4838, "tags": []}, {"Architecture": ["x86_64"], "BestOnDemandHourPriceRegion": "ap-south-1", "CanonicalName": "c7i-flex.2xlarge", "InstanceType": "c7i-flex.2xlarge", "Memory": 16384, "ModifiedDate": "2024-11-13 00:29:56", "PaymentType": "OnDemand", "PricePerHour": 0.52269, "tags": []}], "spot": [{"Architecture": ["x86_64"], "BestOnDemandHourPriceRegion": "ap-south-1", "CanonicalName": "c7i-flex.2xlarge", "InstanceType": "c7i-flex.2xlarge", "Memory": 16384, "ModifiedDate": "2024-11-13 00:29:56", "PaymentType": "OnDemand", "PricePerHour": 0.05579038461538462, "tags": ["cheapest"]}, {"Architecture": ["x86_64"], "BestOnDemandHourPriceRegion": "us-east-1", "CanonicalName": "t2.2xlarge", "InstanceType": "t2.2xlarge", "Memory": 32768, "ModifiedDate": "2024-11-13 00:29:56", "PaymentType": "OnDemand", "PricePerHour": 0.06118571428571429, "tags": []}, {"Architecture": ["x86_64"], "BestOnDemandHourPriceRegion": "us-east-1", "CanonicalName": "inf1.2xlarge", "InstanceType": "inf1.2xlarge", "Memory": 16384, "ModifiedDate": "2024-11-13 00:29:56", "PaymentType": "OnDemand", "PricePerHour": 0.06157173913043478, "tags": []}, {"Architecture": ["x86_64"], "BestOnDemandHourPriceRegion": "ap-southeast-5", "CanonicalName": "c6id.2xlarge", "InstanceType": "c6id.2xlarge", "Memory": 16384, "ModifiedDate": "2024-11-13 00:29:56", "PaymentType": "OnDemand", "PricePerHour": 0.06933055555555555, "tags": []}]}}