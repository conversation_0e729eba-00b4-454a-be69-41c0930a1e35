intent: postgres
flavor: k8s
version: "0.1"
description: Adds Postgres module of flavor kubernetes
clouds:
  - aws
  - azure
  - gcp
  - kubernetes
metadata:
  title: Metadata of Mongo
  type: object
  properties:
    namespace:
      type: string
      title: Namespace
      description: Namespace in which Mongo should be deployed
      default: default
spec:
  title: K8s
  type: object
  properties:
    postgres_version:
      type: string
      title: Postgres Version
      description: Specifies the Postgres version to use.
      minLength: 1
      x-ui-placeholder: "e.g., '13.3.0' or '15.3.0'"
      enum:
        - "9.6.9"
        - "10.9.0"
        - "11.9.0"
        - "12.9.0"
        - "13.9.0"
        - "14.9.0"
        - "15.3.0"
        - "15.8.0"
        - "16.0.0"
        - "16.4.0"
        - "17.0.0"
      x-ui-error-message: "Please select a supported Postgres version."
    size:
      type: object
      title: Size
      description: Configuration for writer and reader instances.
      properties:
        reader:
          type: object
          title: Reader
          description: Reader instance settings.
          properties:
            cpu:
              type: string
              title: CPU
              description: Number of CPU cores required.
              minLength: 1
              pattern: "^([1-9]|[12][0-9]|3[0-2])$|^(3[0-1][0-9]{3}|[1-2][0-9]{4}|[1-9][0-9]{0,3}|32000)m$"
              x-ui-compare:
                field: spec.size.reader.cpu_limit
                comparator: "<="
                x-ui-error-message: "CPU cannot be more than CPU limit"
              x-ui-placeholder: "e.g., '500m' or '1'"
              x-ui-error-message: "Value doesn't match pattern, it should be number ranging from 1 to 32 or 1m to 32000m"
            memory:
              type: string
              title: Memory
              description: Amount of memory required.
              minLength: 1
              pattern: "^(0\\.[1-9]|[1-9](\\.[0-9]+)?|[1-5][0-9](\\.[0-9]+)?|6[0-4])Gi$|^([1-9](\\.[0-9]+)?|[1-9][0-9]{1,3}(\\.[0-9]+)?|[1-5][0-9]{4}(\\.[0-9]+)?|6[0-3][0-9]{3}(\\.[0-9]+)?|64000)Mi$"
              x-ui-compare:
                field: spec.size.reader.memory_limit
                comparator: "<="
                x-ui-error-message: "Memory cannot be more than memory limit"
              x-ui-placeholder: "e.g., '800Mi' or '1.5Gi'"
              x-ui-error-message: "Value doesn't match pattern, it should be number ranging from 1Gi to 64Gi or 1Mi to 64000Mi"
            cpu_limit:
              type: string
              title: CPU Limit
              description: Set a maximum limit on CPU utilization.
              minLength: 1
              pattern: "^([1-9]|[12][0-9]|3[0-2])$|^(3[0-1][0-9]{3}|[1-2][0-9]{4}|[1-9][0-9]{0,3}|32000)m$"
              x-ui-compare:
                field: spec.size.reader.cpu
                comparator: ">="
                x-ui-error-message: "CPU limit cannot be less than CPU"
              x-ui-placeholder: "e.g., '500m' or '1'"
              x-ui-error-message: "Value doesn't match pattern, it should be number ranging from 1 to 32 or 1m to 32000m"
            memory_limit:
              type: string
              title: Memory Limit
              description: Set a maximum limit on memory utilization.
              minLength: 1
              pattern: "^(0\\.[1-9]|[1-9](\\.[0-9]+)?|[1-5][0-9](\\.[0-9]+)?|6[0-4])Gi$|^([1-9](\\.[0-9]+)?|[1-9][0-9]{1,3}(\\.[0-9]+)?|[1-5][0-9]{4}(\\.[0-9]+)?|6[0-3][0-9]{3}(\\.[0-9]+)?|64000)Mi$"
              x-ui-compare:
                field: spec.size.reader.memory
                comparator: ">="
                x-ui-error-message: "Memory limit cannot be less than memory"
              x-ui-placeholder: "e.g., '800Mi' or '1.5Gi'"
              x-ui-error-message: "Value doesn't match pattern, it should be number ranging from 1Gi to 64Gi or 1Mi to 64000Mi"
            volume:
              type: string
              title: Volume
              description: The size of the volume.
              minLength: 1
              pattern: "^[0-9]+[EiKMGTP]i?$"
              x-ui-placeholder: "e.g., '100Mi' or '50Gi'"
              x-ui-error-message: "Volume must be specified in the correct format with integer only (e.g., '10Gi' or '50Gi')."
            instance_count:
              type: integer
              title: Instance Count
              description: Number of instances to create.
              minimum: 0
              maximum: 10
              x-ui-placeholder: "e.g., 2"
              x-ui-error-message: "Instance count must be between 1 and 10."
          required:
            - instance_count
            - cpu
            - memory
          x-ui-order:
            - instance_count
            - cpu
            - memory
            - cpu_limit
            - memory_limit
            - volume
        writer:
          type: object
          title: Writer
          description: Writer instance settings.
          properties:
            cpu:
              type: string
              title: CPU
              description: Number of CPU cores required.
              minLength: 1
              pattern: "^([1-9]|[12][0-9]|3[0-2])$|^(3[0-1][0-9]{3}|[1-2][0-9]{4}|[1-9][0-9]{0,3}|32000)m$"
              x-ui-compare:
                field: spec.size.writer.cpu_limit
                comparator: "<="
                x-ui-error-message: "CPU cannot be more than CPU limit"
              x-ui-placeholder: "e.g., '500m' or '1'"
              x-ui-error-message: "Value doesn't match pattern, it should be number ranging from 1 to 32 or 1m to 32000m"
            memory:
              type: string
              title: Memory
              description: Amount of memory required.
              minLength: 1
              pattern: "^(0\\.[1-9]|[1-9](\\.[0-9]+)?|[1-5][0-9](\\.[0-9]+)?|6[0-4])Gi$|^([1-9](\\.[0-9]+)?|[1-9][0-9]{1,3}(\\.[0-9]+)?|[1-5][0-9]{4}(\\.[0-9]+)?|6[0-3][0-9]{3}(\\.[0-9]+)?|64000)Mi$"
              x-ui-compare:
                field: spec.size.writer.memory_limit
                comparator: "<="
                x-ui-error-message: "Memory cannot be more than memory limit"
              x-ui-placeholder: "e.g., '800Mi' or '1.5Gi'"
              x-ui-error-message: "Value doesn't match pattern, it should be number ranging from 1Gi to 64Gi or 1Mi to 64000Mi"
            cpu_limit:
              type: string
              title: CPU Limit
              description: Set a maximum limit on CPU utilization.
              minLength: 1
              pattern: "^([1-9]|[12][0-9]|3[0-2])$|^(3[0-1][0-9]{3}|[1-2][0-9]{4}|[1-9][0-9]{0,3}|32000)m$"
              x-ui-compare:
                field: spec.size.writer.cpu
                comparator: ">="
                x-ui-error-message: "CPU limit cannot be less than CPU"
              x-ui-placeholder: "e.g., '500m' or '1'"
              x-ui-error-message: "Value doesn't match pattern, it should be number ranging from 1 to 32 or 1m to 32000m"
            memory_limit:
              type: string
              title: Memory Limit
              description: Set a maximum limit on memory utilization.
              minLength: 1
              pattern: "^(0\\.[1-9]|[1-9](\\.[0-9]+)?|[1-5][0-9](\\.[0-9]+)?|6[0-4])Gi$|^([1-9](\\.[0-9]+)?|[1-9][0-9]{1,3}(\\.[0-9]+)?|[1-5][0-9]{4}(\\.[0-9]+)?|6[0-3][0-9]{3}(\\.[0-9]+)?|64000)Mi$"
              x-ui-compare:
                field: spec.size.writer.memory
                comparator: ">="
                x-ui-error-message: "Memory limit cannot be less than memory"
              x-ui-placeholder: "e.g., '800Mi' or '1.5Gi'"
              x-ui-error-message: "Value doesn't match pattern, it should be number ranging from 1Gi to 64Gi or 1Mi to 64000Mi"
            volume:
              type: string
              title: Volume
              description: The size of the volume.
              minLength: 1
              pattern: "^[0-9]+[EiKMGTP]i?$"
              x-ui-placeholder: "e.g., '100Mi' or '50Gi'"
              x-ui-error-message: "Volume must be specified in the correct format with integer only (e.g., '10Gi' or '50Gi')."
          required:
            - cpu
            - memory
          x-ui-order:
            - cpu
            - memory
            - cpu_limit
            - memory_limit
            - volume
      required:
        - writer
      x-ui-order:
        - writer
        - reader
    db_names:
      type: array
      title: Additional Database Names
      description: List of additional databases to be created
      pattern: "^(?!.*[_-]{2,})[a-zA-Z0-9]+([-_][a-zA-Z0-9]+)*(, [a-zA-Z0-9]+([-_][a-zA-Z0-9]+)*)*$|^(?!.*[_-]{2,})[a-zA-Z0-9]+([-_][a-zA-Z0-9]+)*(,[a-zA-Z0-9]+([-_][a-zA-Z0-9]+)*)*$|^(?!.*[_-]{2,})[a-zA-Z0-9]+([-_][a-zA-Z0-9]+)*(, [a-zA-Z0-9]+([-_][a-zA-Z0-9])+)*$"
      x-ui-error-message: "Database names must start with an alphanumeric character and should be separated by commas"
    db_schemas:
      type: object
      title: Additional Database Schemas
      description: Map of additonal schemas to be created.
      patternProperties:
        keyPattern: "^[a-zA-Z0-9-_]+$"
        title: Database Schemas
        description: Database schema row item which contains schema name and database name in which schema will be created.
        properties:
          db:
            title: Database Name
            type: string
            description: Name of the database under which schema will be created.
            pattern: "^[a-zA-Z0-9]+([-_][a-zA-Z0-9]+)*$"
            x-ui-error-message: Database name must start with an alphabet or underscore and should contain only alphanumeric characters and underscores
          schema:
            title: Database Schema
            type: string
            description: Name of the schema which needs to be created.
            pattern: "^[a-zA-Z0-9]+([-_][a-zA-Z0-9]+)*$"
        required:
          - db
          - schema
  required:
    - size
    - postgres_version
  x-ui-order:
    - postgres_version
    - size
    - db_names
    - db_schemas
sample:
  $schema: >-
    https://facets-cloud.github.io/facets-schemas/schemas/postgres/postgres.schema.json
  flavor: k8s
  metadata: {}
  kind: postgres
  disabled: true
  version: "0.1"
  spec:
    postgres_version: "15.3.0"
    size:
      writer:
        cpu: "250m"
        memory: "500Mi"
        cpu_limit: "500m"
        memory_limit: "1Gi"
        volume: "10Gi"
      reader:
        cpu: "250m"
        memory: "256Mi"
        cpu_limit: "500m"
        memory_limit: "512Mi"
        volume: "10Gi"
        instance_count: 0
