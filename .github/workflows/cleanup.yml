name: Cleanup Preview Modules

on:
  workflow_dispatch:
  pull_request:
    types: [ closed ]
    branches: [ 'master' ]

jobs:
  cleanup:
    if: (github.event_name == 'pull_request' && github.event.pull_request.merged == true) || github.event_name == 'workflow_dispatch'
    runs-on: iac-arc

    steps:
      - name: Checkout code
        uses: actions/checkout@v2

      - name: Create control planes JSON file
        id: set_control_planes
        run: |
          set -e
          set -o pipefail
          JSON_CONTENT='${{ vars.DEV_CONTROL_PLANES }}'
          echo "$JSON_CONTENT" > control_planes.json

      - name: Create secrets JSON file
        id: set_secrets
        run: |
          echo '${{ toJson(secrets) }}' > secrets.json

      - name: Execute cleanup script
        run: |
          set -e
          set -o pipefail
          export GITHUB_REF_NAME="${{ github.event_name == 'workflow_dispatch' && github.ref_name || github.event.pull_request.head.ref }}"
          echo "Exporting GITHUB_REF_NAME: $GITHUB_REF_NAME"
          python3 .github/workflows/innersourcing/cleanup.py