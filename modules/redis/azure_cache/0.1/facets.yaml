intent: redis
flavor: azure_cache
version: 0.1
description: Adds Redis module of flavor azure_cache
clouds:
  - azure
spec:
  title: Redis Azure Cache
  type: object
  properties:
    authenticated:
      type: boolean
      title: Authenticated
      description: Enables password protection.
    persistence_enabled:
      type: boolean
      title: Persistence Enabled
      description: Ensures data is preserved across restarts.
    redis_version:
      type: string
      title: Redis Version
      description: Specifies the Redis version to use.
      enum:
        - '4.0'
        - '6.0'
      x-ui-error-message: "Please select a supported Redis version."
    size:
      type: object
      title: Size
      description: Configuration for datastore components.
      properties:
        reader:
          type: object
          title: Reader
          description: Settings for reader instances.
          x-ui-toggle: true
          properties:
            instance_count:
              type: integer
              title: Instance Count
              description: Number of reader instances to create.
              minimum: 0
              maximum: 3
              x-ui-error-message: "Instance count must be between 0 and 3."
          required:
            - instance_count
      required:
        - reader
      x-ui-order:
        - reader
  required:
    - size
    - authenticated
    - persistence_enabled
    - redis_version
  x-ui-order:
    - authenticated
    - persistence_enabled
    - redis_version
    - size
sample:
  $schema: 'https://facets-cloud.github.io/facets-schemas/schemas/redis/redis.schema.json'
  flavor: azure_cache
  metadata:
    tags:
      managed-by: facets
  kind: redis
  disabled: true
  version: '0.1'
  spec:
    authenticated: true
    redis_version: '6.0'
    persistence_enabled: true
    size:
      reader:
        instance_count: 1
