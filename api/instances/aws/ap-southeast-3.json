{"16Cores": {"onDemand": [{"Architecture": ["x86_64"], "BestOnDemandHourPriceRegion": "ap-south-1", "CanonicalName": "c5.4xlarge", "InstanceType": "c5.4xlarge", "Memory": 32768, "ModifiedDate": "2024-11-13 00:29:56", "PaymentType": "OnDemand", "PricePerHour": 0.784, "tags": ["cheapest"]}, {"Architecture": ["x86_64"], "BestOnDemandHourPriceRegion": "us-east-1", "CanonicalName": "c5d.4xlarge", "InstanceType": "c5d.4xlarge", "Memory": 32768, "ModifiedDate": "2024-11-13 00:29:56", "PaymentType": "OnDemand", "PricePerHour": 0.896, "tags": []}, {"Architecture": ["x86_64"], "BestOnDemandHourPriceRegion": "us-east-1", "CanonicalName": "m5.4xlarge", "InstanceType": "m5.4xlarge", "Memory": 65536, "ModifiedDate": "2024-11-13 00:29:56", "PaymentType": "OnDemand", "PricePerHour": 0.96, "tags": []}, {"Architecture": ["x86_64"], "BestOnDemandHourPriceRegion": "us-east-1", "CanonicalName": "m6i.4xlarge", "InstanceType": "m6i.4xlarge", "Memory": 65536, "ModifiedDate": "2024-11-13 00:29:56", "PaymentType": "OnDemand", "PricePerHour": 0.96, "tags": []}], "spot": [{"Architecture": ["x86_64"], "BestOnDemandHourPriceRegion": "ap-south-1", "CanonicalName": "c5n.4xlarge", "InstanceType": "c5n.4xlarge", "Memory": 43008, "ModifiedDate": "2024-11-13 00:29:56", "PaymentType": "OnDemand", "PricePerHour": 0.15630789473684212, "tags": ["cheapest"]}, {"Architecture": ["x86_64"], "BestOnDemandHourPriceRegion": "us-east-1", "CanonicalName": "i4i.4xlarge", "InstanceType": "i4i.4xlarge", "Memory": 131072, "ModifiedDate": "2024-11-13 00:29:56", "PaymentType": "OnDemand", "PricePerHour": 0.1723957446808511, "tags": []}, {"Architecture": ["x86_64"], "BestOnDemandHourPriceRegion": "ap-south-1", "CanonicalName": "c5.4xlarge", "InstanceType": "c5.4xlarge", "Memory": 32768, "ModifiedDate": "2024-11-13 00:29:56", "PaymentType": "OnDemand", "PricePerHour": 0.17885370370370368, "tags": []}, {"Architecture": ["x86_64"], "BestOnDemandHourPriceRegion": "us-east-1", "CanonicalName": "r7i.4xlarge", "InstanceType": "r7i.4xlarge", "Memory": 131072, "ModifiedDate": "2024-11-13 00:29:56", "PaymentType": "OnDemand", "PricePerHour": 0.1960586956521739, "tags": []}]}, "4Cores": {"onDemand": [{"Architecture": ["x86_64"], "BestOnDemandHourPriceRegion": "ap-south-1", "CanonicalName": "c5.xlarge", "InstanceType": "c5.xlarge", "Memory": 8192, "ModifiedDate": "2024-11-13 00:29:56", "PaymentType": "OnDemand", "PricePerHour": 0.196, "tags": ["cheapest"]}, {"Architecture": ["x86_64"], "BestOnDemandHourPriceRegion": "us-east-1", "CanonicalName": "t3.xlarge", "InstanceType": "t3.xlarge", "Memory": 16384, "ModifiedDate": "2024-11-13 00:29:56", "PaymentType": "OnDemand", "PricePerHour": 0.2112, "tags": []}, {"Architecture": ["x86_64"], "BestOnDemandHourPriceRegion": "us-east-1", "CanonicalName": "c5d.xlarge", "InstanceType": "c5d.xlarge", "Memory": 8192, "ModifiedDate": "2024-11-13 00:29:56", "PaymentType": "OnDemand", "PricePerHour": 0.224, "tags": []}, {"Architecture": ["x86_64"], "BestOnDemandHourPriceRegion": "us-east-1", "CanonicalName": "m5.xlarge", "InstanceType": "m5.xlarge", "Memory": 16384, "ModifiedDate": "2024-11-13 00:29:56", "PaymentType": "OnDemand", "PricePerHour": 0.24, "tags": []}], "spot": [{"Architecture": ["x86_64"], "BestOnDemandHourPriceRegion": "us-east-1", "CanonicalName": "m5d.xlarge", "InstanceType": "m5d.xlarge", "Memory": 16384, "ModifiedDate": "2024-11-13 00:29:56", "PaymentType": "OnDemand", "PricePerHour": 0.02885348837209302, "tags": ["cheapest"]}, {"Architecture": ["x86_64"], "BestOnDemandHourPriceRegion": "us-east-1", "CanonicalName": "i4i.xlarge", "InstanceType": "i4i.xlarge", "Memory": 32768, "ModifiedDate": "2024-11-13 00:29:56", "PaymentType": "OnDemand", "PricePerHour": 0.05005263157894737, "tags": []}, {"Architecture": ["x86_64"], "BestOnDemandHourPriceRegion": "us-east-1", "CanonicalName": "i3.xlarge", "InstanceType": "i3.xlarge", "Memory": 31232, "ModifiedDate": "2024-11-13 00:29:56", "PaymentType": "OnDemand", "PricePerHour": 0.050314, "tags": []}, {"Architecture": ["x86_64"], "BestOnDemandHourPriceRegion": "ap-south-1", "CanonicalName": "c5n.xlarge", "InstanceType": "c5n.xlarge", "Memory": 10752, "ModifiedDate": "2024-11-13 00:29:56", "PaymentType": "OnDemand", "PricePerHour": 0.05851219512195122, "tags": []}]}, "8Cores": {"onDemand": [{"Architecture": ["x86_64"], "BestOnDemandHourPriceRegion": "ap-south-1", "CanonicalName": "c5.2xlarge", "InstanceType": "c5.2xlarge", "Memory": 16384, "ModifiedDate": "2024-11-13 00:29:56", "PaymentType": "OnDemand", "PricePerHour": 0.392, "tags": ["cheapest"]}, {"Architecture": ["x86_64"], "BestOnDemandHourPriceRegion": "us-east-1", "CanonicalName": "t3.2xlarge", "InstanceType": "t3.2xlarge", "Memory": 32768, "ModifiedDate": "2024-11-13 00:29:56", "PaymentType": "OnDemand", "PricePerHour": 0.4224, "tags": []}, {"Architecture": ["x86_64"], "BestOnDemandHourPriceRegion": "us-east-1", "CanonicalName": "c5d.2xlarge", "InstanceType": "c5d.2xlarge", "Memory": 16384, "ModifiedDate": "2024-11-13 00:29:56", "PaymentType": "OnDemand", "PricePerHour": 0.448, "tags": []}, {"Architecture": ["x86_64"], "BestOnDemandHourPriceRegion": "us-east-1", "CanonicalName": "m5.2xlarge", "InstanceType": "m5.2xlarge", "Memory": 32768, "ModifiedDate": "2024-11-13 00:29:56", "PaymentType": "OnDemand", "PricePerHour": 0.48, "tags": []}], "spot": [{"Architecture": ["x86_64"], "BestOnDemandHourPriceRegion": "us-east-1", "CanonicalName": "t3.2xlarge", "InstanceType": "t3.2xlarge", "Memory": 32768, "ModifiedDate": "2024-11-13 00:29:56", "PaymentType": "OnDemand", "PricePerHour": 0.05564444444444444, "tags": ["cheapest"]}, {"Architecture": ["x86_64"], "BestOnDemandHourPriceRegion": "us-east-1", "CanonicalName": "i3.2xlarge", "InstanceType": "i3.2xlarge", "Memory": 62464, "ModifiedDate": "2024-11-13 00:29:56", "PaymentType": "OnDemand", "PricePerHour": 0.08554090909090908, "tags": []}, {"Architecture": ["x86_64"], "BestOnDemandHourPriceRegion": "us-east-1", "CanonicalName": "i4i.2xlarge", "InstanceType": "i4i.2xlarge", "Memory": 65536, "ModifiedDate": "2024-11-13 00:29:56", "PaymentType": "OnDemand", "PricePerHour": 0.08578039215686276, "tags": []}, {"Architecture": ["x86_64"], "BestOnDemandHourPriceRegion": "us-east-1", "CanonicalName": "i3en.2xlarge", "InstanceType": "i3en.2xlarge", "Memory": 65536, "ModifiedDate": "2024-11-13 00:29:56", "PaymentType": "OnDemand", "PricePerHour": 0.10964444444444445, "tags": []}]}}