{"kind": "configuration", "for": "prometheus", "version": "latest", "ui": {"base_resource": true}, "spec": {"alertmanager": {"size": {"cpu": "50m", "memory": "0.25Gi"}}, "grafana": {"size": {"cpu": "100m", "memory": "0.25Gi"}}, "prometheus": {"size": {"cpu": "200m", "memory": "2Gi"}}, "prometheus-operator": {"size": {"cpu": "100m", "memory": "0.25Gi"}}, "pushgateway": {"size": {"cpu": "50m", "memory": "0.25Gi"}}}}