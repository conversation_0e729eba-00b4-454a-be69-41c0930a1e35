intent: ecs_service
flavor: default
version: 0.2
clouds:
  - aws
artifact_inputs:
  primary:
    attribute_path: "spec.release.image"
    artifact_type: "docker_image"
inputs:
  network_details:
    optional: false
    type: "@outputs/aws_vpc"
    default:
      resource_type: network
      resource_name: default
  ecs_details:
    optional: false
    type: "@outputs/ecs_cluster"
    default:
      resource_type: ecs_cluster
      resource_name: default
spec:
  type: object
  properties:
    runtime:
      type: object
      properties:
        ports:
          type: object
          title: Ports
          x-ui-override-disable: true
          description: Port mappings
          patternProperties:
            keyPattern: "^[0-9]+[m]?$"
            type: object
            title: Port Name
            description: Define port allocation to facilitate communication with service
            properties:
              port:
                type: string
                title: Port
                description: Port on which application is running
                pattern: "^(?!0$)([1-9][0-9]{0,3}|[1-5][0-9]{4}|6[0-4][0-9]{3}|65[0-4][0-9]{2}|655[0-2][0-9]|6553[0-5])$"
                x-ui-placeholder: "Enter the port number to expose for your application container"
                x-ui-error-message: "Value doesn't match pattern, it should be number ranging from 1-65535"
              service_port:
                type: string
                title: Service Port
                description: Port from which application service can be exposed
                pattern: "^(?!0$)([1-9][0-9]{0,3}|[1-5][0-9]{4}|6[0-4][0-9]{3}|65[0-4][0-9]{2}|655[0-2][0-9]|6553[0-5])$"
                x-ui-placeholder: "Enter the port number to expose for your application service"
                x-ui-error-message: "Value doesn't match pattern, it should be number ranging from 1-65535"
              protocol:
                type: string
                title: Protocol
                description: TCP or UDP
                enum:
                  - tcp
                  - udp
            required:
              - protocol
              - port
        size:
          type: object
          title: Size
          properties:
            cpu:
              type: integer
              title: CPU
            memory:
              type: integer
              title: Memory
        command:
          type: string
        args:
          type: object
          title: Arguments
          x-ui-override-disable: true
          description: Command-line arguments for the container
          patternProperties:
            keyPattern: "^[a-zA-Z0-9_-]+$"
            type: object
            title: Argument Name
            description: Define command-line arguments for the container
            properties:
              argument:
                type: string
                title: Argument
                description: Command-line argument value
            required:
              - argument
        readonly_root_filesystem:
          type: boolean
    release:
      type: object
      properties:
        image:
          type: string
        registry_name:
          title: Registry Name
          type: string
          description: "Container Registry Name. <br>Note: To pull images from an ECR in another AWS account, ensure that the source ECR repository is shared with your account by updating its repository policy to allow cross-account access.</br>"
          x-ui-placeholder: "[Optional] Specify only if using an image from custom container registry"
          x-ui-typeable: true
          x-ui-api-source:
            endpoint: "/cc-ui/v1/artifactories"
            method: GET
            params:
              includeContent: false
            labelKey: name
            valueKey: name
    env:
      title: Environment Variables
      description: Map of environment variables passed to the container.
      type: object
      patternProperties:
        "^[a-zA-Z][a-zA-Z0-9_-.]*$":
          title: Environment Variable
          description: Environment Variable
          type: string
      x-ui-yaml-editor: true
    cloud_permissions:
      type: object
      title: Cloud Permissions
      description: Assign roles, define access levels, and enforce conditions for managing resource security and compliance
      x-ui-toggle: true
      properties:
        aws:
          type: object
          title: AWS
          x-ui-toggle: true
          properties:
            iam_policies:
              type: object
              title: IAM policies
              description: Define IAM policies to manage permissions, control-access and secure resources
              patternProperties:
                keyPattern: "^[a-zA-Z0-9_.-]*$"
                title: policy_name
                properties:
                  arn:
                    title: ARN
                    type: string
                    pattern: '^(arn:aws:iam::(\d{12}|aws):policy\/[A-Za-z0-9+=,.@\-_]+|\$\{[A-Za-z0-9._-]+\})$'
                    x-ui-placeholder: "IAM policy example. Eg: arn:aws:iam::123456789012:policy/policy1"
                    x-ui-error-message: "Value doesn't match pattern, accepted value pattern Eg: arn:aws:iam::123456789012:policy/policy1"
                    x-ui-output-type: "iam_policy_arn"
                    x-ui-typeable: true
                required:
                  - arn
  required:
    - runtime
    - release
    - env
sample:
  kind: "ecs_service"
  flavor: "default"
  version: "0.2"
  spec:
    runtime:
      ports:
        http:
          port: "80"
          service_port: "80"
          protocol: "tcp"
      size:
        cpu: 512
        memory: 1
      readonly_root_filesystem: true
    env:
      APP_ENV: "production"
      LOG_LEVEL: "info"
