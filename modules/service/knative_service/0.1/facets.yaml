intent: service
flavor: knative_service
version: "0.1"
description: Adds service module of flavor knative
clouds:
  - aws
  - azure
  - gcp
  - kubernetes
artifact_inputs:
  primary:
    attribute_path: "spec.release.image"
    artifact_type: "docker_image"

inputs:
  kubernetes_details:
    optional: false
    type: "@outputs/kubernetes"
    default:
      resource_type: kubernetes_cluster
      resource_name: default
  artifactories:
    optional: true
    type: "@outputs/artifactories"
    default:
      resource_type: artifactories
      resource_name: default
spec:
  title: K8s flavor of service
  type: object
  properties:
    release:
      title: Release
      description: Manage version releases to deploy updates and maintain software integrity
      type: object
      properties:
        image:
          type: string
          title: Image
          description: The docker image of the application that you want to run
          x-ui-skip: true
          x-ui-error-message: "Value doesn't match pattern for a container image"
        build:
          type: object
          title: Build
          description: These contains the build objects required for running the containers
          properties:
            artifactory:
              type: string
              title: Artifactory
              description: Parent artifactory name
            name:
              type: string
              title: Name
              description: Name of the artifactory
            pull_policy:
              type: string
              title: Pull Policy
              description: ImagePullPolicy
              enum:
                - IfNotPresent
                - Always
                - Never
          required: [ "artifactory", "name" ]
          x-ui-skip: true
    env:
      title: Environment Variables
      description: Map of environment variables passed to init container.
      type: object
      patternPorperties:
        title: Evironment Variable
        description: Environment Variable
        keyPattern: "^[a-zA-Z][a-zA-Z0-9_-.]*$"
        type: string
      x-ui-yaml-editor: true
    knative_annotation:
      type: object
      title: Knative serving annotation
      description: Annotation for Knative service annotation
      patternPorperties:
        title: Evironment Variable
        description: Environment Variable
        keyPattern: "^[a-zA-Z][a-zA-Z0-9_-.]*$"
        type: string
      x-ui-yaml-editor: true
    runtime:
      type: object
      title: Runtime (Main Application Container)
      description: Customize service runtime settings like ports, healthchecks etc.
      properties:
        ports:
          type: object
          title: Ports
          x-ui-override-disable: true
          description: Port mappings
          patternProperties:
            type: object
            title: Port Name
            description: Define port allocation to facilitate communication with service
            keyPattern: "^[0-9]+[m]?$"
            properties:
              name:
                type: string
                title: Port Name
                description: Port name for metrics
                x-ui-dynamic-enum: spec.runtime.ports.*
                x-ui-disable-tooltip: "No Ports Available"
              port:
                type: string
                title: Port
                description: Port on which application is running
                pattern: "^(?!0$)([1-9][0-9]{0,3}|[1-5][0-9]{4}|6[0-4][0-9]{3}|65[0-4][0-9]{2}|655[0-2][0-9]|6553[0-5])$"
                x-ui-unique: true
                x-ui-placeholder: "Enter the port number to expose for your application container"
                x-ui-error-message: "Value doesn't match pattern, it should be number ranging from 1-65535"
              service_port:
                type: string
                title: Service Port
                description: Port from which application service can be exposed
                pattern: "^(?!0$)([1-9][0-9]{0,3}|[1-5][0-9]{4}|6[0-4][0-9]{3}|65[0-4][0-9]{2}|655[0-2][0-9]|6553[0-5])$"
                x-ui-unique: true
                x-ui-placeholder: "Enter the port number to expose for your application service"
                x-ui-error-message: "Value doesn't match pattern, it should be number ranging from 1-65535"
              protocol:
                type: string
                title: Protocol
                description: TCP or UDP
                enum:
                  - tcp
                  - udp
            required:
              - protocol
              - port
      required:
        - ports
      x-ui-order:
        - ports
    cloud_permissions:
      type: object
      title: Cloud Permissions
      description: Assign roles, define access levels, and enforce conditions for managing resource security and compliance
      x-ui-toggle: true
      properties:
        aws:
          type: object
          title: AWS
          x-ui-toggle: true
          properties:
            iam_policies:
              type: object
              title: IAM policies
              description: Define IAM policies to manage permissions, control-access and secure resources
              patternProperties:
                keyPattern: "^[a-zA-Z0-9_.-]*$"
                title: policy_name
                properties:
                  arn:
                    title: ARN
                    type: string
                    pattern: '^(arn:aws:iam::(\d{12}|aws):policy\/[A-Za-z0-9+=,.@\-_]+|\$\{[A-Za-z0-9._-]+\})$'
                    x-ui-placeholder: "IAM policy example. Eg: arn:aws:iam::123456789012:policy/policy1"
                    x-ui-error-message: "Value doesn't match pattern, accepted value pattern Eg: arn:aws:iam::123456789012:policy/policy1"
                    x-ui-output-type: "iam_policy_arn"
                    x-ui-typeable: true
                required:
                  - arn
  required: []
  x-ui-order:
    - runtime
    - knative_annotation
    - env
    - cloud_permissions
outputs:
  default:
    type: "@outputs/service"

sample:
  $schema: >-
    https://facets-cloud.github.io/facets-schemas/schemas/service/service.schema.json
  flavor: knative_service
  metadata:
    labels: {}
  kind: service
  disabled: true
  version: "0.1"
  spec:
    env:
      LOG_LEVEL: INFO
    runtime:
      ports: {}
    knative_annotation: {}
    release: {}