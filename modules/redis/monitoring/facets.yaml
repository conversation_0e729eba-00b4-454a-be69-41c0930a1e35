intent: redis_monitoring
flavor: k8s
version: "0.1"
clouds:
  - aws
  - kubernetes
  - azure
  - gcp
description: Adds Monitoring to Redis
inputs:
  default:
    type: "@output/redis"
    adds_capability: true
spec:
  title: Redis Monitoring
  type: object
  properties:
    alerts:
      type: object
      title: Alerts
      description: Alerts configuration
      properties:
        redis_down:
          type: object
          title: Redis Down
          description: Redis Down Alert
          properties:
            disabled:
              type: boolean
              title: Disabled
              description: Toggle to enable or disable this specific alert
            interval:
              type: string
              title: Interval
              description: Time duration before triggering the alert
              pattern: ^(\d+(\.\d+)?(h|m|s|ms|us|µs|ns))+$
              x-ui-placeholder: "eg. 1m5s"
              x-ui-error-message: The time duration must be in a valid format, such as 72h, 3.5m, 1.2s, 500ms, 2h50m, or 1m25s.
            severity:
              type: string
              title: Severity
              description: Categorizes the urgency and impact of an alert
              enum:
                - "critical"
                - "warning"
        redis_out_of_configured_max_memory:
          type: object
          title: Redis Out Of Configured Max Memory
          description: Redis Out Of Configured Max Memory Alert
          properties:
            disabled:
              type: boolean
              title: Disabled
              description: Toggle to enable or disable this specific alert
            interval:
              type: string
              title: Interval
              description: Time duration before triggering the alert
              pattern: ^(\d+(\.\d+)?(h|m|s|ms|us|µs|ns))+$
              x-ui-placeholder: "eg. 1m5s"
              x-ui-error-message: The time duration must be in a valid format, such as 72h, 3.5m, 1.2s, 500ms, 2h50m, or 1m25s.
            severity:
              type: string
              title: Severity
              description: Categorizes the urgency and impact of an alert
              enum:
                - "critical"
                - "warning"
            threshold:
              type: integer
              title: Threshold
              description: The Percentage of maximum memory usage at which alert is triggered
              minimum: 0
              maximum: 100
        redis_too_many_connections:
          type: object
          title: Redis Too Many Connections
          description: Redis Too Many Connections Alert
          properties:
            disabled:
              type: boolean
              title: Disabled
              description: Toggle to enable or disable this specific alert
            interval:
              type: string
              title: Interval
              description: Time duration before triggering the alert
              pattern: ^(\d+(\.\d+)?(h|m|s|ms|us|µs|ns))+$
              x-ui-placeholder: "eg. 1m5s"
              x-ui-error-message: The time duration must be in a valid format, such as 72h, 3.5m, 1.2s, 500ms, 2h50m, or 1m25s.
            severity:
              type: string
              title: Severity
              description: Categorizes the urgency and impact of an alert
              enum:
                - "critical"
                - "warning"
            threshold:
              type: integer
              title: Threshold
              description: The absolute number of connections after which an alert is triggered
              minimum: 1
sample:
  kind: redis_monitoring
  flavor: k8s
  version: "0.1"
  disabled: true
  metadata: {}
  spec:
    alerts:
      redis_down:
        disabled: false
        interval: 10m
        severity: critical
      redis_out_of_configured_max_memory:
        disabled: false
        interval: 5m
        severity: critical
        threshold: 90
      redis_too_many_connections:
        disabled: false
        interval: 5m
        severity: critical
        threshold: 8000
