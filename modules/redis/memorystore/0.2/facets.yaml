intent: redis
flavor: memorystore
version: 0.2
description: Adds redis module of flavor memorystore
clouds:
  - gcp
spec:
  title: Memorystore
  type: object
  properties:
    authenticated:
      type: boolean
      title: Authenticated
      description: Enables password protection.
    persistence_enabled:
      type: boolean
      title: Persistence Enabled
      description: Ensures data is preserved across restarts.
    redis_version:
      type: string
      title: Redis Version
      description: Specifies the Redis version to use.
      enum:
        - '3.2'
        - '4.0'
        - '5.0'
        - '6.x'
        - '7.0'
      x-ui-error-message: "Please select a supported Redis version."
    size:
      type: object
      title: Size
      description: Configuration for datastore components.
      x-ui-order:
        - writer
        - reader
      required:
        - writer
      properties:
        reader:
          type: object
          title: Reader
          description: Settings for reader instances.
          x-ui-toggle: true
          properties:
            instance_count:
              type: integer
              title: Instance Count
              description: Number of reader instances.
              default: 0
              minimum: 0
              maximum: 5
              x-ui-error-message: "Instance count must be a non-negative integer not exceeding 5."
        writer:
          type: object
          title: Writer
          description: Settings for writer instances.
          required:
            - memory
          properties:
            memory:
              type: string
              title: Memory
              description: Memory size in GiB (e.g., '10Gi').
              pattern: "^[0-9]+Gi$"
              x-ui-error-message: "Memory must be specified in Gi format (e.g., '10Gi')."
  required:
    - size
    - authenticated
    - persistence_enabled
    - redis_version
  x-ui-order:
    - authenticated
    - persistence_enabled
    - redis_version
    - size
sample:
  $schema: 'https://facets-cloud.github.io/facets-schemas/schemas/redis/redis.schema.json'
  flavor: memorystore
  metadata: { }
  kind: redis
  disabled: true
  unconfigured: true
  version: '0.2'
  spec:
    authenticated: true
    redis_version: '6.x'
    persistence_enabled: false
    size:
      writer:
        memory: 5Gi
      reader:
        memory: 5Gi
        instance_count: 0
