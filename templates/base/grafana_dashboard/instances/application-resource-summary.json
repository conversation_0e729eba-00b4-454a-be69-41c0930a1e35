{"$schema": "https://facets-cloud.github.io/facets-schemas/schemas/grafana_dashboard/grafana_dashboard.schema.json", "kind": "grafana_dashboard", "flavor": "default", "version": "latest", "lifecycle": "ENVIRONMENT_BOOTSTRAP", "disabled": false, "provided": false, "depends_on": [], "metadata": {"name": "application-resource-summary"}, "ui": {"base_resource": true}, "spec": {"dashboard": {"annotations": {"list": [{"builtIn": 1, "datasource": {"type": "grafana", "uid": "-- <PERSON><PERSON> --"}, "enable": true, "hide": true, "iconColor": "rgba(0, 211, 255, 1)", "name": "Annotations & Alerts", "target": {"limit": 100, "matchAny": false, "tags": [], "type": "dashboard"}, "type": "dashboard"}]}, "editable": true, "fiscalYearStartMonth": 0, "graphTooltip": 0, "links": [], "liveNow": false, "panels": [{"datasource": {"type": "prometheus", "uid": "prometheus"}, "fieldConfig": {"defaults": {"color": {"mode": "thresholds"}, "custom": {"align": "auto", "displayMode": "auto", "filterable": false, "inspect": false}, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": null}, {"color": "red", "value": 80}]}}, "overrides": [{"matcher": {"id": "by<PERSON><PERSON>", "options": "container"}, "properties": [{"id": "displayName", "value": "Application"}]}, {"matcher": {"id": "by<PERSON><PERSON>", "options": "Value #A"}, "properties": [{"id": "displayName", "value": "CPU Requested per Pod"}]}, {"matcher": {"id": "by<PERSON><PERSON>", "options": "Value #B"}, "properties": [{"id": "displayName", "value": "Memory Requested per Pod"}, {"id": "unit", "value": "decgbytes"}]}, {"matcher": {"id": "by<PERSON><PERSON>", "options": "Value #C"}, "properties": [{"id": "displayName", "value": "Pod Count"}]}, {"matcher": {"id": "by<PERSON><PERSON>", "options": "CPUTotal"}, "properties": [{"id": "displayName", "value": "Total CPU Requested"}]}, {"matcher": {"id": "by<PERSON><PERSON>", "options": "MemTotal"}, "properties": [{"id": "custom.width", "value": 224}, {"id": "displayName", "value": "Total Memory Requested"}, {"id": "unit", "value": "decgbytes"}]}, {"matcher": {"id": "by<PERSON><PERSON>", "options": "Pod Count"}, "properties": [{"id": "custom.width", "value": 104}]}, {"matcher": {"id": "by<PERSON><PERSON>", "options": "Value #D"}, "properties": [{"id": "custom.width", "value": 101}, {"id": "displayName", "value": "CPU Recommended per pod"}]}, {"matcher": {"id": "by<PERSON><PERSON>", "options": "Value #E"}, "properties": [{"id": "displayName", "value": "Memory Recommended per pod"}, {"id": "unit", "value": "decgbytes"}]}, {"matcher": {"id": "by<PERSON><PERSON>", "options": "Recommended Memory"}, "properties": [{"id": "custom.width", "value": 257}, {"id": "unit", "value": "decgbytes"}]}, {"matcher": {"id": "by<PERSON><PERSON>", "options": "CPU Recommended per pod"}, "properties": [{"id": "custom.width", "value": 216}]}, {"matcher": {"id": "by<PERSON><PERSON>", "options": "Memory Requested per Pod "}, "properties": [{"id": "custom.width", "value": 196}]}, {"matcher": {"id": "by<PERSON><PERSON>", "options": "Memory Recommended per pod"}, "properties": [{"id": "custom.width", "value": 219}]}]}, "gridPos": {"h": 20, "w": 24, "x": 0, "y": 0}, "id": 2, "options": {"footer": {"enablePagination": false, "fields": [], "reducer": ["sum"], "show": false}, "showHeader": true, "sortBy": [{"desc": true, "displayName": "Total Memory Requested"}]}, "pluginVersion": "9.2.15", "targets": [{"datasource": {"type": "prometheus", "uid": "prometheus"}, "editorMode": "code", "exemplar": false, "expr": "max(kube_pod_container_resource_requests{resource=\"cpu\", namespace=\"default\"} * on(pod,instance) group_left() kube_pod_info{created_by_kind=\"ReplicaSet\"}) by (container)", "format": "table", "instant": true, "legendFormat": "__auto", "range": false, "refId": "A"}, {"datasource": {"type": "prometheus", "uid": "prometheus"}, "editorMode": "code", "exemplar": false, "expr": "max(kube_pod_container_resource_requests{resource=\"memory\"}/(1024*1024*1024)) by (container)", "format": "table", "hide": false, "instant": true, "legendFormat": "__auto", "range": false, "refId": "B"}, {"datasource": {"type": "prometheus", "uid": "prometheus"}, "editorMode": "code", "exemplar": false, "expr": "count(kube_pod_container_info) by (container)", "format": "table", "hide": false, "instant": true, "legendFormat": "__auto", "range": false, "refId": "C"}, {"datasource": {"type": "prometheus", "uid": "prometheus"}, "editorMode": "code", "expr": "max by (container) (kube_verticalpodautoscaler_status_recommendation_containerrecommendations_target{resource=\"cpu\",unit=\"core\"})", "format": "table", "hide": false, "legendFormat": "__auto", "range": true, "refId": "D"}, {"datasource": {"type": "prometheus", "uid": "prometheus"}, "editorMode": "code", "expr": "max by (container) (kube_verticalpodautoscaler_status_recommendation_containerrecommendations_target{resource=\"memory\",unit=\"byte\"}/(1024*1024*1024))", "format": "table", "hide": false, "legendFormat": "__auto", "range": true, "refId": "E"}], "title": "Application Resource Request Details", "transformations": [{"id": "joinByField", "options": {"byField": "container", "mode": "outer"}}, {"id": "filterFieldsByName", "options": {"include": {"names": ["container", "Value #A", "Value #B", "Value #C", "Value #E", "Value #D"]}}}, {"id": "calculateField", "options": {"alias": "CPUTotal", "binary": {"left": "Value #A", "operator": "*", "reducer": "sum", "right": "Value #C"}, "mode": "binary", "reduce": {"reducer": "sum"}}}, {"id": "calculateField", "options": {"alias": "MemTotal", "binary": {"left": "Value #B", "operator": "*", "reducer": "sum", "right": "Value #C"}, "mode": "binary", "reduce": {"reducer": "sum"}}}, {"id": "organize", "options": {"excludeByName": {"Total Recommended CPU": true, "Total Recommended Memory": true, "Value #D": false, "Value #E": false}, "indexByName": {}, "renameByName": {}}}], "type": "table"}], "schemaVersion": 37, "style": "dark", "tags": [], "templating": {"list": []}, "time": {"from": "now-1h", "to": "now"}, "timepicker": {}, "timezone": "", "title": "Application Resource Request Details", "uid": "8X24682hQT2J7KFl", "version": 3, "weekStart": ""}}, "out": {}, "advanced": {"inherit_from_base": true}}