{"16Cores": {"onDemand": [{"Architecture": ["x86_64"], "BestOnDemandHourPriceRegion": "ap-south-1", "CanonicalName": "c6a.4xlarge", "InstanceType": "c6a.4xlarge", "Memory": 32768, "ModifiedDate": "2024-11-13 00:29:56", "PaymentType": "OnDemand", "PricePerHour": 0.7992, "tags": ["cheapest"]}, {"Architecture": ["x86_64"], "BestOnDemandHourPriceRegion": "ap-south-1", "CanonicalName": "c5a.4xlarge", "InstanceType": "c5a.4xlarge", "Memory": 32768, "ModifiedDate": "2024-11-13 00:29:56", "PaymentType": "OnDemand", "PricePerHour": 0.8, "tags": []}, {"Architecture": ["x86_64"], "BestOnDemandHourPriceRegion": "ap-south-1", "CanonicalName": "m5a.4xlarge", "InstanceType": "m5a.4xlarge", "Memory": 65536, "ModifiedDate": "2024-11-13 00:29:56", "PaymentType": "OnDemand", "PricePerHour": 0.864, "tags": []}, {"Architecture": ["x86_64"], "BestOnDemandHourPriceRegion": "ap-south-1", "CanonicalName": "m6a.4xlarge", "InstanceType": "m6a.4xlarge", "Memory": 65536, "ModifiedDate": "2024-11-13 00:29:56", "PaymentType": "OnDemand", "PricePerHour": 0.864, "tags": []}], "spot": [{"Architecture": ["x86_64"], "BestOnDemandHourPriceRegion": "ap-south-1", "CanonicalName": "c6a.4xlarge", "InstanceType": "c6a.4xlarge", "Memory": 32768, "ModifiedDate": "2024-11-13 00:29:56", "PaymentType": "OnDemand", "PricePerHour": 0.29988750000000003, "tags": ["cheapest"]}, {"Architecture": ["x86_64"], "BestOnDemandHourPriceRegion": "ap-south-1", "CanonicalName": "m6a.4xlarge", "InstanceType": "m6a.4xlarge", "Memory": 65536, "ModifiedDate": "2024-11-13 00:29:56", "PaymentType": "OnDemand", "PricePerHour": 0.313686, "tags": []}, {"Architecture": ["x86_64"], "BestOnDemandHourPriceRegion": "ap-southeast-5", "CanonicalName": "c6i.4xlarge", "InstanceType": "c6i.4xlarge", "Memory": 32768, "ModifiedDate": "2024-11-13 00:29:56", "PaymentType": "OnDemand", "PricePerHour": 0.33278199999999997, "tags": []}, {"Architecture": ["x86_64"], "BestOnDemandHourPriceRegion": "ap-south-1", "CanonicalName": "c5.4xlarge", "InstanceType": "c5.4xlarge", "Memory": 32768, "ModifiedDate": "2024-11-13 00:29:56", "PaymentType": "OnDemand", "PricePerHour": 0.33478, "tags": []}]}, "4Cores": {"onDemand": [{"Architecture": ["x86_64"], "BestOnDemandHourPriceRegion": "ap-south-1", "CanonicalName": "t3a.xlarge", "InstanceType": "t3a.xlarge", "Memory": 16384, "ModifiedDate": "2024-11-13 00:29:56", "PaymentType": "OnDemand", "PricePerHour": 0.1901, "tags": ["cheapest"]}, {"Architecture": ["x86_64"], "BestOnDemandHourPriceRegion": "ap-south-1", "CanonicalName": "c6a.xlarge", "InstanceType": "c6a.xlarge", "Memory": 8192, "ModifiedDate": "2024-11-13 00:29:56", "PaymentType": "OnDemand", "PricePerHour": 0.1998, "tags": []}, {"Architecture": ["x86_64"], "BestOnDemandHourPriceRegion": "ap-south-1", "CanonicalName": "c5a.xlarge", "InstanceType": "c5a.xlarge", "Memory": 8192, "ModifiedDate": "2024-11-13 00:29:56", "PaymentType": "OnDemand", "PricePerHour": 0.2, "tags": []}, {"Architecture": ["x86_64"], "BestOnDemandHourPriceRegion": "us-east-1", "CanonicalName": "t3.xlarge", "InstanceType": "t3.xlarge", "Memory": 16384, "ModifiedDate": "2024-11-13 00:29:56", "PaymentType": "OnDemand", "PricePerHour": 0.2112, "tags": []}], "spot": [{"Architecture": ["x86_64"], "BestOnDemandHourPriceRegion": "ap-south-1", "CanonicalName": "t3a.xlarge", "InstanceType": "t3a.xlarge", "Memory": 16384, "ModifiedDate": "2024-11-13 00:29:56", "PaymentType": "OnDemand", "PricePerHour": 0.07172954545454545, "tags": ["cheapest"]}, {"Architecture": ["x86_64"], "BestOnDemandHourPriceRegion": "ap-south-1", "CanonicalName": "c6a.xlarge", "InstanceType": "c6a.xlarge", "Memory": 8192, "ModifiedDate": "2024-11-13 00:29:56", "PaymentType": "OnDemand", "PricePerHour": 0.07824255319148936, "tags": []}, {"Architecture": ["x86_64"], "BestOnDemandHourPriceRegion": "ap-south-1", "CanonicalName": "m6a.xlarge", "InstanceType": "m6a.xlarge", "Memory": 16384, "ModifiedDate": "2024-11-13 00:29:56", "PaymentType": "OnDemand", "PricePerHour": 0.08365777777777778, "tags": []}, {"Architecture": ["x86_64"], "BestOnDemandHourPriceRegion": "us-east-1", "CanonicalName": "t2.xlarge", "InstanceType": "t2.xlarge", "Memory": 16384, "ModifiedDate": "2024-11-13 00:29:56", "PaymentType": "OnDemand", "PricePerHour": 0.08557291666666667, "tags": []}]}, "8Cores": {"onDemand": [{"Architecture": ["x86_64"], "BestOnDemandHourPriceRegion": "ap-south-1", "CanonicalName": "t3a.2xlarge", "InstanceType": "t3a.2xlarge", "Memory": 32768, "ModifiedDate": "2024-11-13 00:29:56", "PaymentType": "OnDemand", "PricePerHour": 0.3802, "tags": ["cheapest"]}, {"Architecture": ["x86_64"], "BestOnDemandHourPriceRegion": "ap-south-1", "CanonicalName": "c6a.2xlarge", "InstanceType": "c6a.2xlarge", "Memory": 16384, "ModifiedDate": "2024-11-13 00:29:56", "PaymentType": "OnDemand", "PricePerHour": 0.3996, "tags": []}, {"Architecture": ["x86_64"], "BestOnDemandHourPriceRegion": "ap-south-1", "CanonicalName": "c5a.2xlarge", "InstanceType": "c5a.2xlarge", "Memory": 16384, "ModifiedDate": "2024-11-13 00:29:56", "PaymentType": "OnDemand", "PricePerHour": 0.4, "tags": []}, {"Architecture": ["x86_64"], "BestOnDemandHourPriceRegion": "us-east-1", "CanonicalName": "t3.2xlarge", "InstanceType": "t3.2xlarge", "Memory": 32768, "ModifiedDate": "2024-11-13 00:29:56", "PaymentType": "OnDemand", "PricePerHour": 0.4224, "tags": []}], "spot": [{"Architecture": ["x86_64"], "BestOnDemandHourPriceRegion": "ap-south-1", "CanonicalName": "t3a.2xlarge", "InstanceType": "t3a.2xlarge", "Memory": 32768, "ModifiedDate": "2024-11-13 00:29:56", "PaymentType": "OnDemand", "PricePerHour": 0.14805625, "tags": ["cheapest"]}, {"Architecture": ["x86_64"], "BestOnDemandHourPriceRegion": "ap-south-1", "CanonicalName": "c6a.2xlarge", "InstanceType": "c6a.2xlarge", "Memory": 16384, "ModifiedDate": "2024-11-13 00:29:56", "PaymentType": "OnDemand", "PricePerHour": 0.15685510204081632, "tags": []}, {"Architecture": ["x86_64"], "BestOnDemandHourPriceRegion": "us-east-1", "CanonicalName": "t3.2xlarge", "InstanceType": "t3.2xlarge", "Memory": 32768, "ModifiedDate": "2024-11-13 00:29:56", "PaymentType": "OnDemand", "PricePerHour": 0.158825, "tags": []}, {"Architecture": ["x86_64"], "BestOnDemandHourPriceRegion": "ap-south-1", "CanonicalName": "c5a.2xlarge", "InstanceType": "c5a.2xlarge", "Memory": 16384, "ModifiedDate": "2024-11-13 00:29:56", "PaymentType": "OnDemand", "PricePerHour": 0.16611599999999999, "tags": []}]}}