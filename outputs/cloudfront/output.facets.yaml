name: cloudfront
out:
  type: object
  title: AWS Cloudfront
  description: AWS Cloudfront
  properties:
    attributes:
      cloudfront_distribution_arn:
        required: true
        type: string
        description: ARN of the CloudFront distribution
      cloudfront_distribution_domain_name:
        required: true
        type: string
        description: Domain name of the CloudFront distribution
      cloudfront_distribution_etag:
        required: true
        type: string
        description: ETag of the CloudFront distribution
      cloudfront_distribution_id:
        required: true
        type: string
        description: ID of the CloudFront distribution
      cloudfront_distribution_last_modified_time:
        required: true
        type: string
        description: Last modified time of the CloudFront distribution
      cloudfront_origin_access_identities:
        required: true
        type: object
        additionalProperties:
          type: string
        description: Origin access identities for the CloudFront distribution
    interfaces: