intent: pvc
flavor: k8s
version: "0.1"
description: Adds pvc module
clouds:
  - aws
  - azure
  - gcp
  - kubernetes
spec:
  title: k8s pvc
  type: object
  properties:
    size:
      type: object
      title: Size
      description: Size of the k8s pvc.
      properties:
        volume:
          type: string
          title: Volume
          description: The size of the volume.
          minLength: 1
          pattern: "^[1-9]+[G]i?$"
          x-ui-placeholder: "e.g., '10Gi' or '50Gi'"
          x-ui-error-message: "Volume must be specified in the correct format (e.g., '10Gi' or '50Gi')."
      required:
        - volume
  required:
    - size
sample:
  $schema: >-
    https://facets-cloud.github.io/facets-schemas/schemas/pvc/pvc.schema.json
  disabled: true
  flavor: k8s
  kind: pvc
  metadata: {}
  version: "0.1"
  spec:
    size:
      volume: "2Gi"
  advanced:
    accessModes:
      - ReadWriteOnce
