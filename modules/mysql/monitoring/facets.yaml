intent: mysql_monitoring
flavor: k8s
version: "0.1"
clouds:
  - aws
  - kubernetes
  - azure
  - gcp
description: Adds Monitoring to Mysql
inputs:
  default:
    type: "@output/mysql"
    adds_capability: true
spec:
  title: Mysql Monitoring
  type: object
  properties:
    alerts:
      type: object
      title: Alerts
      description: Defines alert rules for Mysql monitoring conditions
      properties:
        mysql_down:
          type: object
          title: Mysql Down
          description: Triggers an alert if Mysql becomes unavailable
          properties:
            disabled:
              type: boolean
              title: Disabled
              description: Toggle to enable or disable this specific alert
            interval:
              type: string
              title: Interval
              description: Time duration before triggering the alert
              pattern: ^(\d+(\.\d+)?(h|m|s|ms|us|µs|ns))+$
              x-ui-placeholder: "eg. 1m5s"
              x-ui-error-message: The time duration must be in a valid format, such as 72h, 3.5m, 1.2s, 500ms, 2h50m, or 1m25s.
            severity:
              type: string
              title: Severity
              description: Categorizes the urgency and impact of an alert
              enum:
                - "critical"
                - "warning"
        mysql_too_many_connections:
          type: object
          title: Mysql Too Many Connections
          description: Alerts when Mysql exceeds connection limits
          properties:
            disabled:
              type: boolean
              title: Disabled
              description: Toggle to enable or disable this specific alert
            interval:
              type: string
              title: Interval
              description: Time duration before triggering the alert
              pattern: ^(\d+(\.\d+)?(h|m|s|ms|us|µs|ns))+$
              x-ui-placeholder: "eg. 1m5s"
              x-ui-error-message: The time duration must be in a valid format, such as 72h, 3.5m, 1.2s, 500ms, 2h50m, or 1m25s.
            severity:
              type: string
              title: Severity
              description: Categorizes the urgency and impact of an alert
              enum:
                - "critical"
                - "warning"
            threshold:
              type: integer
              title: Threshold
              description: The Percentage value of maximum mysql connections at which the alert is triggered
              minimum: 0
              maximum: 100
        mysql_restarted:
          type: object
          title: Mysql Restarted
          description: Alerts when Mysql Restarts
          properties:
            disabled:
              type: boolean
              title: Disabled
              description: Toggle to enable or disable this specific alert
            interval:
              type: string
              title: Interval
              description: Time duration before triggering the alert
              pattern: ^(\d+(\.\d+)?(h|m|s|ms|us|µs|ns))+$
              x-ui-placeholder: "eg. 1m5s"
              x-ui-error-message: The time duration must be in a valid format, such as 72h, 3.5m, 1.2s, 500ms, 2h50m, or 1m25s.
            severity:
              type: string
              title: Severity
              description: Categorizes the urgency and impact of an alert
              enum:
                - "critical"
                - "warning"
sample:
  kind: mysql_monitoring
  flavor: k8s
  version: "0.1"
  metadata: {}
  disabled: true
  spec:
    alerts:
      mysql_down:
        disabled: false
        interval: 10m
        severity: critical
      mysql_too_many_connections:
        disabled: false
        interval: 5m
        severity: critical
        threshold: 80
      mysql_restarted:
        disabled: false
        interval: 0m
        severity: critical
