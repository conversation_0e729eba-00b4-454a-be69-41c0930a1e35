{"$schema": "https://facets-cloud.github.io/facets-schemas/schemas/grafana_dashboard/grafana_dashboard.schema.json", "kind": "grafana_dashboard", "flavor": "default", "version": "latest", "lifecycle": "ENVIRONMENT", "disabled": false, "provided": false, "depends_on": [], "metadata": {"name": "node-resource-details"}, "ui": {"base_resource": true}, "spec": {"dashboard": {"__inputs": [], "__elements": {}, "__requires": [{"type": "panel", "id": "gauge", "name": "Gauge", "version": ""}, {"type": "grafana", "id": "grafana", "name": "<PERSON><PERSON>", "version": "9.2.15"}, {"type": "datasource", "id": "prometheus", "name": "Prometheus", "version": "1.0.0"}, {"type": "panel", "id": "table", "name": "Table", "version": ""}], "annotations": {"list": [{"builtIn": 1, "datasource": {"type": "grafana", "uid": "-- <PERSON><PERSON> --"}, "enable": true, "hide": true, "iconColor": "rgba(0, 211, 255, 1)", "name": "Annotations & Alerts", "target": {"limit": 100, "matchAny": false, "tags": [], "type": "dashboard"}, "type": "dashboard"}]}, "editable": true, "fiscalYearStartMonth": 0, "graphTooltip": 0, "links": [], "liveNow": false, "panels": [{"datasource": {"type": "prometheus", "uid": "prometheus"}, "fieldConfig": {"defaults": {"color": {"mode": "thresholds"}, "mappings": [], "max": 1, "min": 0, "thresholds": {"mode": "absolute", "steps": [{"color": "dark-red"}, {"color": "#EAB839", "value": 0.4}, {"color": "green", "value": 0.6}]}}, "overrides": [{"matcher": {"id": "by<PERSON><PERSON>", "options": "Value #A / Value #B"}, "properties": [{"id": "unit", "value": "percentunit"}, {"id": "displayName", "value": "CPU Allocation"}]}, {"matcher": {"id": "by<PERSON><PERSON>", "options": "Value #C / Value #D"}, "properties": [{"id": "unit", "value": "percentunit"}, {"id": "displayName", "value": "Memory Allocation"}]}]}, "gridPos": {"h": 8, "w": 12, "x": 6, "y": 0}, "id": 4, "options": {"orientation": "auto", "reduceOptions": {"calcs": ["lastNotNull"], "fields": "", "values": false}, "showThresholdLabels": false, "showThresholdMarkers": true}, "pluginVersion": "9.2.15", "targets": [{"datasource": {"type": "prometheus", "uid": "prometheus"}, "editorMode": "code", "exemplar": false, "expr": "sum(kube_pod_container_resource_requests{resource=\"cpu\", node=\"$node\"})", "format": "table", "instant": true, "legendFormat": "__auto", "range": false, "refId": "A"}, {"datasource": {"type": "prometheus", "uid": "prometheus"}, "editorMode": "code", "exemplar": false, "expr": "kube_node_status_allocatable{resource=\"cpu\", node=\"$node\"}", "format": "table", "hide": false, "instant": true, "legendFormat": "__auto", "range": false, "refId": "B"}, {"datasource": {"type": "prometheus", "uid": "prometheus"}, "editorMode": "code", "exemplar": false, "expr": "sum(kube_pod_container_resource_requests{resource=\"memory\", node=\"$node\"})", "format": "table", "hide": false, "instant": true, "legendFormat": "__auto", "range": false, "refId": "C"}, {"datasource": {"type": "prometheus", "uid": "prometheus"}, "editorMode": "code", "exemplar": false, "expr": "kube_node_status_allocatable{resource=\"memory\", node=\"$node\"}", "format": "table", "hide": false, "instant": true, "legendFormat": "__auto", "range": false, "refId": "D"}], "title": "Node Summary", "transformations": [{"id": "calculateField", "options": {"binary": {"left": "Value #A", "operator": "/", "reducer": "sum", "right": "Value #B"}, "mode": "binary", "reduce": {"reducer": "sum"}}}, {"id": "calculateField", "options": {"binary": {"left": "Value #C", "operator": "/", "reducer": "sum", "right": "Value #D"}, "mode": "binary", "reduce": {"reducer": "sum"}}}, {"id": "filterFieldsByName", "options": {"include": {"names": ["Value #A / Value #B", "Value #C / Value #D"]}}}], "type": "gauge"}, {"datasource": {"type": "prometheus", "uid": "prometheus"}, "fieldConfig": {"defaults": {"color": {"mode": "thresholds"}, "custom": {"align": "center", "displayMode": "auto", "inspect": false}, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green"}, {"color": "red", "value": 80}]}}, "overrides": [{"matcher": {"id": "by<PERSON><PERSON>", "options": "Value #A"}, "properties": [{"id": "displayName", "value": "CPU Requests"}]}, {"matcher": {"id": "by<PERSON><PERSON>", "options": "Value #B"}, "properties": [{"id": "unit", "value": "decbytes"}, {"id": "displayName", "value": "Memory Requests"}]}]}, "gridPos": {"h": 13, "w": 24, "x": 0, "y": 8}, "id": 2, "options": {"footer": {"fields": "", "reducer": ["sum"], "show": false}, "showHeader": true, "sortBy": [{"desc": true, "displayName": "CPU Requests"}]}, "pluginVersion": "9.2.15", "targets": [{"datasource": {"type": "prometheus", "uid": "prometheus"}, "editorMode": "code", "exemplar": false, "expr": "sum(kube_pod_container_resource_requests{resource=\"cpu\", node=\"$node\"}) by (pod)", "format": "table", "instant": true, "legendFormat": "__auto", "range": false, "refId": "A"}, {"datasource": {"type": "prometheus", "uid": "prometheus"}, "editorMode": "code", "exemplar": false, "expr": "sum(kube_pod_container_resource_requests{resource=\"memory\", node=\"$node\"}) by (pod)", "format": "table", "hide": false, "instant": true, "legendFormat": "__auto", "range": false, "refId": "B"}], "title": "Panel Title", "transformations": [{"id": "joinByField", "options": {"byField": "pod", "mode": "inner"}}, {"id": "filterFieldsByName", "options": {"include": {"names": ["pod", "Value #A", "Value #B"]}}}], "type": "table"}], "schemaVersion": 37, "style": "dark", "tags": [], "templating": {"list": [{"current": {}, "datasource": {"type": "prometheus", "uid": "prometheus"}, "definition": "label_values(kube_node_info, node)", "description": "", "hide": 0, "includeAll": false, "label": "Node", "multi": false, "name": "node", "options": [], "query": {"query": "label_values(kube_node_info, node)", "refId": "StandardVariableQuery"}, "refresh": 1, "regex": "", "skipUrlSync": false, "sort": 0, "type": "query"}]}, "time": {"from": "now-6h", "to": "now"}, "timepicker": {}, "timezone": "", "title": "Node Resource Allocation", "uid": "p4lLcnDIz", "version": 8, "weekStart": ""}}, "out": {}, "advanced": {"inherit_from_base": true, "default": {"preserve_uid": true}}}