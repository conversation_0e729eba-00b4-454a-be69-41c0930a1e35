intent: ingress
flavor: gcp_alb
version: '0.1'
description: Adds ingress - gcp_alb flavor
clouds:
- gcp
sample:
  kind: ingress
  $schema: https://facets-cloud.github.io/facets-schemas/schemas/ingress/ingress.schema.json
  flavor: gcp_alb
  disabled: true
  version: '0.1'
  metadata:
    annotations: {}
  spec:
    private: false
    basic_auth: false
    grpc: false
    domains: {}
    rules:
      grafana1:
        service_name: prometheus-operator-grafana
        path: /
        port: 80
        domain_prefix: grafana1
        annotations:
          app: ''
        disable_auth: false
    force_ssl_redirection: true
