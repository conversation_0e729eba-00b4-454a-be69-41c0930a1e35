{"kind": "grafana_dashboard", "flavor": "default", "version": "latest", "lifecycle": "ENVIRONMENT", "disabled": true, "provided": false, "depends_on": [], "metadata": {"name": "win-apps-dashboard"}, "ui": {"base_resource": true}, "spec": {"dashboard": {"annotations": {"list": [{"builtIn": 1, "datasource": {"type": "datasource", "uid": "grafana"}, "enable": true, "hide": true, "iconColor": "rgba(0, 211, 255, 1)", "name": "Annotations & Alerts", "target": {"limit": 100, "matchAny": false, "tags": [], "type": "dashboard"}, "type": "dashboard"}]}, "editable": true, "fiscalYearStartMonth": 0, "graphTooltip": 0, "links": [], "liveNow": false, "panels": [{"aliasColors": {}, "bars": false, "dashLength": 10, "dashes": false, "datasource": {"type": "prometheus", "uid": "prometheus"}, "fieldConfig": {"defaults": {"links": []}, "overrides": []}, "fill": 0, "fillGradient": 0, "gridPos": {"h": 7, "w": 24, "x": 0, "y": 0}, "hiddenSeries": false, "id": 9, "legend": {"alignAsTable": true, "avg": true, "current": false, "max": true, "min": false, "rightSide": true, "show": true, "total": false, "values": true}, "lines": true, "linewidth": 2, "nullPointMode": "null", "options": {"alertThreshold": true}, "percentage": false, "pluginVersion": "9.3.1", "pointradius": 2, "points": false, "renderer": "flot", "seriesOverrides": [], "spaceLength": 10, "stack": false, "steppedLine": false, "targets": [{"datasource": {"type": "prometheus", "uid": "prometheus"}, "expr": "sum(rate(windows_container_cpu_usage_seconds_total[5m]) * on(container_id) group_left(namespace, pod, container) kube_pod_container_info{container=\"$appName\",pod=~\"$appName-[a-zA-Z0-9]*-[a-zA-Z0-9]*\"}) by (pod)", "hide": false, "instant": false, "interval": "", "legendFormat": "used-{{ pod }}", "refId": "A"}, {"datasource": {"type": "prometheus", "uid": "prometheus"}, "editorMode": "code", "expr": "container_spec_cpu_quota{image=\"\", pod=~\"$appName-[a-zA-Z0-9]*-[a-zA-Z0-9]*\"}/100000", "format": "time_series", "hide": false, "instant": false, "interval": "", "intervalFactor": 1, "legendFormat": "limit-{{ pod }}", "refId": "B"}], "thresholds": [], "timeRegions": [], "title": "Container CPU Cores Used", "tooltip": {"shared": true, "sort": 1, "value_type": "individual"}, "type": "graph", "xaxis": {"mode": "time", "show": true, "values": []}, "yaxes": [{"$$hashKey": "object:3125", "format": "short", "logBase": 1, "show": true}, {"$$hashKey": "object:3126", "format": "short", "logBase": 1, "show": true}], "yaxis": {"align": false}}, {"aliasColors": {}, "bars": false, "dashLength": 10, "dashes": false, "datasource": {"type": "prometheus", "uid": "prometheus"}, "fieldConfig": {"defaults": {"links": []}, "overrides": []}, "fill": 0, "fillGradient": 0, "gridPos": {"h": 7, "w": 24, "x": 0, "y": 7}, "hiddenSeries": false, "id": 11, "legend": {"alignAsTable": true, "avg": true, "current": false, "max": true, "min": false, "rightSide": true, "show": true, "total": false, "values": true}, "lines": true, "linewidth": 2, "nullPointMode": "null", "options": {"alertThreshold": true}, "percentage": false, "pluginVersion": "9.3.1", "pointradius": 2, "points": false, "renderer": "flot", "seriesOverrides": [], "spaceLength": 10, "stack": false, "steppedLine": false, "targets": [{"datasource": {"type": "prometheus", "uid": "prometheus"}, "editorMode": "code", "expr": "sum(windows_container_memory_usage_private_working_set_bytes * on(container_id) group_left(namespace, pod, container) kube_pod_container_info{pod=~\"$appName-[a-zA-Z0-9]*-[a-zA-Z0-9]*\", job=\"kube-state-metrics\", image!=\"\", container!=\"POD\"}/1073741824) by (pod)", "hide": false, "interval": "", "intervalFactor": 1, "legendFormat": "used-{{ pod }}", "range": true, "refId": "A"}, {"datasource": {"type": "prometheus", "uid": "prometheus"}, "editorMode": "code", "expr": "sum(kube_pod_container_resource_limits{pod=~\"$appName-[a-zA-Z0-9]*-[a-zA-Z0-9]*\", job=\"kube-state-metrics\", container!=\"POD\", resource=\"memory\"}/1073741824) by (pod)", "hide": false, "interval": "", "intervalFactor": 1, "legendFormat": "limit-{{ pod }}", "range": true, "refId": "B"}], "thresholds": [], "timeRegions": [], "title": "Container GBytes of Memory Used", "tooltip": {"shared": true, "sort": 0, "value_type": "individual"}, "type": "graph", "xaxis": {"mode": "time", "show": true, "values": []}, "yaxes": [{"$$hashKey": "object:4978", "format": "short", "logBase": 1, "show": true}, {"$$hashKey": "object:4979", "format": "short", "logBase": 1, "show": true}], "yaxis": {"align": false}}, {"aliasColors": {}, "bars": false, "dashLength": 10, "dashes": false, "datasource": {"type": "prometheus", "uid": "prometheus"}, "fieldConfig": {"defaults": {"links": []}, "overrides": []}, "fill": 0, "fillGradient": 0, "gridPos": {"h": 7, "w": 24, "x": 0, "y": 14}, "hiddenSeries": false, "id": 23, "legend": {"alignAsTable": true, "avg": true, "current": false, "max": true, "min": false, "rightSide": true, "show": true, "sort": "max", "sortDesc": true, "total": false, "values": true}, "lines": true, "linewidth": 2, "nullPointMode": "null", "options": {"alertThreshold": true}, "percentage": false, "pluginVersion": "9.3.1", "pointradius": 2, "points": false, "renderer": "flot", "seriesOverrides": [], "spaceLength": 10, "stack": false, "steppedLine": false, "targets": [{"datasource": {"type": "prometheus", "uid": "prometheus"}, "editorMode": "code", "expr": "100 * max(windows_container_memory_usage_private_working_set_bytes * on(container_id) group_left(namespace, pod, container) kube_pod_container_info{pod=~\"$appName-[a-zA-Z0-9]*-[a-zA-Z0-9]*\", job=\"kube-state-metrics\", image!=\"\", container!=\"POD\"} / on (container, pod) kube_pod_container_resource_limits{pod=~\"$appName-[a-zA-Z0-9]*-[a-zA-Z0-9]*\", resource=\"memory\", job=\"kube-state-metrics\", container!=\"POD\"}) by (container)", "hide": false, "interval": "", "intervalFactor": 1, "legendFormat": "{{ pod }}", "range": true, "refId": "A"}], "thresholds": [], "timeRegions": [], "title": "Container Memory Usage - % Used vs Limit", "tooltip": {"shared": true, "sort": 0, "value_type": "individual"}, "type": "graph", "xaxis": {"mode": "time", "show": true, "values": []}, "yaxes": [{"$$hashKey": "object:4978", "format": "short", "logBase": 1, "show": true}, {"$$hashKey": "object:4979", "format": "short", "logBase": 1, "show": true}], "yaxis": {"align": false}}, {"aliasColors": {}, "bars": false, "dashLength": 10, "dashes": false, "datasource": {"type": "prometheus", "uid": "prometheus"}, "fieldConfig": {"defaults": {"links": []}, "overrides": []}, "fill": 0, "fillGradient": 0, "gridPos": {"h": 8, "w": 12, "x": 0, "y": 21}, "hiddenSeries": false, "id": 13, "legend": {"avg": false, "current": false, "max": true, "min": false, "show": true, "total": false, "values": true}, "lines": true, "linewidth": 2, "nullPointMode": "null", "options": {"alertThreshold": true}, "percentage": false, "pluginVersion": "9.3.1", "pointradius": 2, "points": false, "renderer": "flot", "seriesOverrides": [], "spaceLength": 10, "stack": false, "steppedLine": false, "targets": [{"datasource": {"type": "prometheus", "uid": "prometheus"}, "editorMode": "code", "expr": "container_fs_usage_bytes{pod=~\"$appName-[a-zA-Z0-9]*-[a-zA-Z0-9]*\",device=\"/dev/nvme0n1p1\", container!=\"POD\"}/(1024*1024*1024)", "hide": false, "interval": "", "legendFormat": "{{ pod }}", "range": true, "refId": "A"}], "thresholds": [], "timeRegions": [], "title": "Pod Root volume usage (Gi)", "tooltip": {"shared": true, "sort": 0, "value_type": "individual"}, "type": "graph", "xaxis": {"mode": "time", "show": true, "values": []}, "yaxes": [{"$$hashKey": "object:7436", "format": "short", "logBase": 1, "show": true}, {"$$hashKey": "object:7437", "format": "short", "logBase": 1, "show": true}], "yaxis": {"align": false}}, {"aliasColors": {}, "bars": false, "dashLength": 10, "dashes": false, "datasource": {"type": "prometheus", "uid": "prometheus"}, "fieldConfig": {"defaults": {"links": []}, "overrides": []}, "fill": 0, "fillGradient": 0, "gridPos": {"h": 8, "w": 12, "x": 12, "y": 21}, "hiddenSeries": false, "id": 7, "legend": {"avg": false, "current": false, "max": false, "min": false, "show": true, "total": false, "values": false}, "lines": true, "linewidth": 2, "nullPointMode": "null", "options": {"alertThreshold": true}, "percentage": false, "pluginVersion": "9.3.1", "pointradius": 2, "points": false, "renderer": "flot", "seriesOverrides": [], "spaceLength": 10, "stack": false, "steppedLine": false, "targets": [{"datasource": {"type": "prometheus", "uid": "prometheus"}, "editorMode": "code", "expr": "kube_pod_container_status_restarts_total{pod=~\"$appName-[a-zA-Z0-9]*-[a-zA-Z0-9]*\"} > 0", "hide": false, "interval": "", "legendFormat": "{{ pod }}", "range": true, "refId": "A"}], "thresholds": [], "timeRegions": [], "title": "Container Restarts", "tooltip": {"shared": true, "sort": 0, "value_type": "individual"}, "type": "graph", "xaxis": {"mode": "time", "show": true, "values": []}, "yaxes": [{"$$hashKey": "object:15059", "format": "short", "logBase": 1, "show": true}, {"$$hashKey": "object:15060", "format": "short", "logBase": 1, "show": true}], "yaxis": {"align": false}}, {"aliasColors": {}, "bars": false, "dashLength": 10, "dashes": false, "datasource": {"type": "prometheus", "uid": "prometheus"}, "fieldConfig": {"defaults": {"links": []}, "overrides": []}, "fill": 0, "fillGradient": 0, "gridPos": {"h": 7, "w": 24, "x": 0, "y": 29}, "hiddenSeries": false, "id": 17, "legend": {"avg": false, "current": false, "max": false, "min": false, "show": true, "total": false, "values": false}, "lines": true, "linewidth": 2, "links": [], "nullPointMode": "null", "options": {"alertThreshold": true}, "percentage": false, "pluginVersion": "9.3.1", "pointradius": 2, "points": false, "renderer": "flot", "seriesOverrides": [], "spaceLength": 10, "stack": false, "steppedLine": true, "targets": [{"datasource": {"type": "prometheus", "uid": "prometheus"}, "editorMode": "code", "expr": "kube_pod_status_phase{phase!=\"Running\",phase!=\"Succeeded\", pod=~\"$appName-[a-zA-Z0-9]*-[a-zA-Z0-9]*\"} > 0", "interval": "", "legendFormat": "{{ pod  }}-{{ phase }}", "range": true, "refId": "A"}], "thresholds": [], "timeRegions": [], "title": "Pod Not Ready", "tooltip": {"shared": true, "sort": 0, "value_type": "individual"}, "type": "graph", "xaxis": {"mode": "time", "show": true, "values": []}, "yaxes": [{"$$hashKey": "object:2621", "format": "short", "logBase": 1, "show": true}, {"$$hashKey": "object:2622", "format": "short", "logBase": 1, "show": true}], "yaxis": {"align": false}}, {"aliasColors": {}, "bars": false, "dashLength": 10, "dashes": false, "datasource": {"type": "prometheus", "uid": "prometheus"}, "fieldConfig": {"defaults": {"links": []}, "overrides": []}, "fill": 0, "fillGradient": 0, "gridPos": {"h": 8, "w": 24, "x": 0, "y": 36}, "hiddenSeries": false, "id": 21, "legend": {"avg": false, "current": false, "max": true, "min": true, "show": true, "total": false, "values": true}, "lines": true, "linewidth": 2, "nullPointMode": "null", "options": {"alertThreshold": true}, "percentage": false, "pluginVersion": "9.3.1", "pointradius": 2, "points": false, "renderer": "flot", "seriesOverrides": [], "spaceLength": 10, "stack": false, "steppedLine": false, "targets": [{"datasource": {"type": "prometheus", "uid": "prometheus"}, "expr": "kube_deployment_status_replicas_available{deployment=~\"$appName\"}", "hide": false, "interval": "", "legendFormat": "{{ deployment }}", "refId": "A"}], "thresholds": [], "timeRegions": [], "title": "Active Pods by Deployment", "tooltip": {"shared": true, "sort": 0, "value_type": "individual"}, "type": "graph", "xaxis": {"mode": "time", "show": true, "values": []}, "yaxes": [{"$$hashKey": "object:5751", "format": "short", "logBase": 1, "show": true}, {"$$hashKey": "object:5752", "format": "short", "logBase": 1, "show": true}], "yaxis": {"align": false}}], "refresh": false, "schemaVersion": 37, "style": "dark", "tags": ["Facets"], "templating": {"list": [{"current": {"selected": false, "text": "windowsop", "value": "windowsop"}, "hide": 0, "label": "Application Name", "name": "appName", "options": [{"selected": true, "text": "windowsop", "value": "windowsop"}], "query": "windowsop", "skipUrlSync": false, "type": "textbox"}]}, "time": {"from": "now-1h", "to": "now"}, "timepicker": {"refresh_intervals": ["5s", "10s", "30s", "1m", "5m", "15m", "30m", "1h", "2h", "1d"]}, "timezone": "browser", "title": "Kubernetes Windows Application Overview", "version": 1}}, "advanced": {"inherit_from_base": true}}