intent: postgres_monitoring
flavor: k8s
version: "0.1"
clouds:
  - aws
  - kubernetes
  - azure
  - gcp
description: Adds Monitoring to Postgres
inputs:
  postgres_outputs:
    type: "@output/postgres"
    adds_capability: true
spec:
  title: Postgres Monitoring
  type: object
  properties:
    alerts:
      type: object
      title: Alerts
      description: Alerts configuration
      properties:
        postgresql_down:
          type: object
          title: PostgreSQL Down
          description: PostgreSQL Down Alert
          properties:
            disabled:
              type: boolean
              title: Disabled
              description: Disable or Enable Alert
            interval:
              type: string
              title: Interval
              description: The duration an alert condition must persist before the alert is triggered
              pattern: ^(\d+(\.\d+)?(h|m|s|ms|us|µs|ns))+$
              x-ui-placeholder: "eg. 1m5s"
              x-ui-error-message: The time duration must be in a valid format, such as 72h, 3.5m, 1.2s, 500ms, 2h50m, or 1m25s.
            severity:
              type: string
              title: Severity
              description: The Severity to categorize the urgency or impact level of an alert, aiding in appropriate response prioritization
        postgresql_too_many_dead_tuples:
          type: object
          title: Postgresql too many dead tuples
          description: Postgresql too many dead tuples Alert
          properties:
            disabled:
              type: boolean
              title: Disabled
              description: Disable or Enable Alert
            interval:
              type: string
              title: Interval
              description: The duration an alert condition must persist before the alert is triggered
              pattern: ^(\d+(\.\d+)?(h|m|s|ms|us|µs|ns))+$
              x-ui-placeholder: "eg. 1m5s"
              x-ui-error-message: The time duration must be in a valid format, such as 72h, 3.5m, 1.2s, 500ms, 2h50m, or 1m25s.
            severity:
              type: string
              title: Severity
              description: The Severity to categorize the urgency or impact level of an alert, aiding in appropriate response prioritization
            threshold:
              type: string
              title: Threshold
              description: The percentage of dead tuples compared to total tuples (live + dead) at which the alert is triggered
        postgresql_too_many_connections:
          type: object
          title: PostgreSQL Too Many Connections
          description: PostgreSQL Too Many Connections Alert
          properties:
            disabled:
              type: boolean
              title: Disabled
              description: Disable or Enable Alert
            interval:
              type: string
              title: Interval
              description: The duration an alert condition must persist before the alert is triggered
              pattern: ^(\d+(\.\d+)?(h|m|s|ms|us|µs|ns))+$
              x-ui-placeholder: "eg. 1m5s"
              x-ui-error-message: The time duration must be in a valid format, such as 72h, 3.5m, 1.2s, 500ms, 2h50m, or 1m25s.
            severity:
              type: string
              title: Severity
              description: The Severity to categorize the urgency or impact level of an alert, aiding in appropriate response prioritization
            threshold:
              type: integer
              title: Threshold
              description: The percentage of maximum allowable PostgreSQL connections in use at which the alert is triggered
              minimum: 0
              maximum: 100
sample:
  kind: postgres_monitoring
  flavor: k8s
  version: "0.1"
  metadata: {}
  disabled: true
  spec:
    exporter:
      disabled: false
      include_databases: []
    alerts:
      postgresql_down:
        disabled: false
        interval: 10m
        severity: critical
      postgresql_too_many_dead_tuples:
        disabled: false
        interval: 5m
        severity: critical
        threshold: 90
      postgresql_too_many_connections:
        disabled: false
        interval: 5m
        severity: critical
        threshold: 80
