{"kind": "grafana_dashboard", "conditional_on_intent": "service", "flavor": "default", "version": "latest", "lifecycle": "ENVIRONMENT", "disabled": false, "provided": false, "depends_on": [], "metadata": {"name": "Application-overview-dashboard"}, "ui": {"base_resource": true}, "spec": {"dashboard": {"annotations": {"list": [{"builtIn": 1, "datasource": {"type": "grafana", "uid": "-- <PERSON><PERSON> --"}, "enable": true, "hide": true, "iconColor": "rgba(0, 211, 255, 1)", "name": "Annotations & Alerts", "target": {"limit": 100, "matchAny": false, "tags": [], "type": "dashboard"}, "type": "dashboard"}]}, "description": "New Application level dashboard ", "editable": true, "fiscalYearStartMonth": 0, "graphTooltip": 0, "links": [], "liveNow": false, "panels": [{"datasource": {"type": "prometheus", "uid": "prometheus"}, "description": "", "fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}, "custom": {"axisCenteredZero": false, "axisColorMode": "text", "axisLabel": "CPU", "axisPlacement": "auto", "axisSoftMin": 0, "barAlignment": 0, "drawStyle": "line", "fillOpacity": 0, "gradientMode": "none", "hideFrom": {"legend": false, "tooltip": false, "viz": false}, "lineInterpolation": "linear", "lineStyle": {"fill": "solid"}, "lineWidth": 1, "pointSize": 3, "scaleDistribution": {"type": "linear"}, "showPoints": "auto", "spanNulls": false, "stacking": {"group": "A", "mode": "none"}, "thresholdsStyle": {"mode": "off"}}, "mappings": [], "thresholds": {"mode": "percentage", "steps": [{"color": "green", "value": null}, {"color": "#EAB839", "value": 70}, {"color": "red", "value": 90}]}, "unit": "none"}, "overrides": [{"matcher": {"id": "by<PERSON><PERSON>", "options": "limit"}, "properties": [{"id": "color", "value": {"fixedColor": "#F2495C", "mode": "fixed"}}]}, {"matcher": {"id": "byFrameRefID", "options": "A"}, "properties": [{"id": "custom.lineStyle", "value": {"dash": [10, 10], "fill": "dash"}}]}]}, "gridPos": {"h": 10, "w": 12, "x": 0, "y": 0}, "id": 2, "options": {"legend": {"calcs": ["max"], "displayMode": "table", "placement": "bottom", "showLegend": true}, "tooltip": {"mode": "multi", "sort": "none"}}, "targets": [{"datasource": {"type": "prometheus", "uid": "prometheus"}, "editorMode": "code", "exemplar": false, "expr": "avg(rate(container_cpu_usage_seconds_total{namespace=~\"$namespace\",endpoint=\"https-metrics\",pod=~\"$pod\",image!=\"\", container!=\"POD\"}[2m])* 1000) ", "format": "time_series", "hide": false, "instant": false, "legendFormat": "avg-usage", "range": true, "refId": "C"}, {"datasource": {"type": "prometheus", "uid": "prometheus"}, "editorMode": "code", "expr": "max(rate(container_cpu_usage_seconds_total{namespace=~\"$namespace\",endpoint=\"https-metrics\",pod=~\"$pod\",image!=\"\", container!=\"POD\"}[2m])* 1000) ", "hide": false, "legendFormat": "max-usage", "range": true, "refId": "E"}, {"datasource": {"type": "prometheus", "uid": "prometheus"}, "editorMode": "code", "expr": "max(kube_pod_container_resource_limits{namespace=~\"$namespace\",endpoint=\"http\",pod=~\"$pod\",container=~\"$container\",resource=\"cpu\",unit=\"core\"})*1000", "hide": false, "legendFormat": "limit", "range": true, "refId": "A"}, {"datasource": {"type": "prometheus", "uid": "prometheus"}, "editorMode": "code", "expr": "max(kube_pod_container_resource_requests{namespace=~\"$namespace\",endpoint=\"http\",pod=~\"$pod\",container=~\"$container\",resource=\"cpu\",unit=\"core\"})*1000", "hide": false, "legendFormat": "requested", "range": true, "refId": "B"}], "title": "CPU Utilisation ", "type": "timeseries"}, {"datasource": {"type": "prometheus", "uid": "prometheus"}, "description": "", "fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}, "custom": {"axisCenteredZero": false, "axisColorMode": "text", "axisLabel": "", "axisPlacement": "auto", "axisSoftMin": 0, "barAlignment": 0, "drawStyle": "line", "fillOpacity": 0, "gradientMode": "none", "hideFrom": {"legend": false, "tooltip": false, "viz": false}, "lineInterpolation": "linear", "lineStyle": {"fill": "solid"}, "lineWidth": 1, "pointSize": 5, "scaleDistribution": {"type": "linear"}, "showPoints": "auto", "spanNulls": false, "stacking": {"group": "A", "mode": "none"}, "thresholdsStyle": {"mode": "off"}}, "mappings": [], "thresholds": {"mode": "percentage", "steps": [{"color": "green", "value": null}, {"color": "#EAB839", "value": 70}, {"color": "red", "value": 90}]}, "unit": "bytes"}, "overrides": [{"matcher": {"id": "byFrameRefID", "options": "D"}, "properties": [{"id": "custom.lineStyle", "value": {"dash": [10, 10], "fill": "dash"}}]}, {"matcher": {"id": "by<PERSON><PERSON>", "options": "limit"}, "properties": [{"id": "color", "value": {"fixedColor": "dark-red", "mode": "fixed"}}]}, {"matcher": {"id": "by<PERSON><PERSON>", "options": "avg-usage"}, "properties": [{"id": "color", "value": {"fixedColor": "green", "mode": "fixed"}}]}, {"matcher": {"id": "by<PERSON><PERSON>", "options": "requested"}, "properties": [{"id": "color", "value": {"fixedColor": "orange", "mode": "fixed"}}]}]}, "gridPos": {"h": 10, "w": 12, "x": 12, "y": 0}, "id": 26, "options": {"legend": {"calcs": ["max"], "displayMode": "table", "placement": "bottom", "showLegend": true}, "tooltip": {"mode": "multi", "sort": "none"}}, "targets": [{"datasource": {"type": "prometheus", "uid": "prometheus"}, "editorMode": "code", "expr": "max(kube_pod_container_resource_requests{namespace=~\"$namespace\",endpoint=\"http\",pod=~\"$pod\",container=~\"$container\",resource=\"memory\",unit=\"byte\",container!=\"POD\"})", "hide": false, "legendFormat": "requested", "range": true, "refId": "B"}, {"datasource": {"type": "prometheus", "uid": "prometheus"}, "editorMode": "code", "expr": "max(container_memory_usage_bytes{namespace=~\"$namespace\", pod=~\"$pod\", container=~\"$container\",endpoint=\"https-metrics\",image!=\"\", container!=\"POD\"})", "hide": false, "legendFormat": "max-usage", "range": true, "refId": "C"}, {"datasource": {"type": "prometheus", "uid": "prometheus"}, "editorMode": "code", "expr": "avg(container_memory_usage_bytes{namespace=~\"$namespace\", pod=~\"$pod\", container=~\"$container\",endpoint=\"https-metrics\",image!=\"\", container!=\"POD\"})", "hide": false, "legendFormat": "avg-usage", "range": true, "refId": "A"}, {"datasource": {"type": "prometheus", "uid": "prometheus"}, "editorMode": "code", "expr": "max(kube_pod_container_resource_limits{namespace=~\"$namespace\",endpoint=\"http\",pod=~\"$pod\",container=~\"$container\",resource=\"memory\",unit=\"byte\",container!=\"POD\"})", "hide": false, "legendFormat": "limit", "range": true, "refId": "D"}], "title": "Memory Utilisation ", "type": "timeseries"}, {"datasource": {"type": "prometheus", "uid": "prometheus"}, "description": "This gives the cpu recommendation and the cap limits for each services", "fieldConfig": {"defaults": {"color": {"mode": "thresholds"}, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": null}, {"color": "red", "value": 80}]}, "unit": " m"}, "overrides": []}, "gridPos": {"h": 6, "w": 24, "x": 0, "y": 10}, "id": 39, "options": {"colorMode": "none", "graphMode": "none", "justifyMode": "auto", "orientation": "auto", "reduceOptions": {"calcs": ["lastNotNull"], "fields": "", "values": false}, "textMode": "auto"}, "pluginVersion": "9.2.15", "targets": [{"datasource": {"type": "prometheus", "uid": "prometheus"}, "editorMode": "code", "expr": "max (kube_pod_container_resource_requests{namespace=~\"$namespace\",endpoint=\"http\",pod=~\"$pod\",container=~\"$container\",resource=\"cpu\",unit=\"core\"} * 1000 )", "hide": false, "legendFormat": "Current requests", "range": true, "refId": "C"}, {"datasource": {"type": "prometheus", "uid": "prometheus"}, "editorMode": "code", "expr": "max(kube_pod_container_resource_limits{namespace=~\"$namespace\",endpoint=\"http\",pod=~\"$pod\",container=~\"$container\",resource=\"cpu\",unit=\"core\"}*1000)", "hide": false, "legendFormat": "Current limits", "range": true, "refId": "D"}, {"datasource": {"type": "prometheus", "uid": "prometheus"}, "editorMode": "code", "expr": "max(kube_verticalpodautoscaler_status_recommendation_containerrecommendations_target{container=\"$service\",resource=\"cpu\", namespace=\"$namespace\"} * 1000)", "hide": false, "legendFormat": "Recommended requests", "range": true, "refId": "E"}, {"datasource": {"type": "prometheus", "uid": "prometheus"}, "editorMode": "code", "expr": "max(kube_verticalpodautoscaler_status_recommendation_containerrecommendations_upperbound{container=\"$service\", resource=\"cpu\", namespace=\"$namespace\"} * 1000)", "hide": false, "legendFormat": "Recommended limits", "range": true, "refId": "A"}], "title": "VPA - CPU recommendations", "type": "stat"}, {"datasource": {"type": "prometheus", "uid": "prometheus"}, "description": "This gives the memory recommendation for the specific service", "fieldConfig": {"defaults": {"color": {"mode": "thresholds"}, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": null}, {"color": "red", "value": 80}]}, "unit": "decbytes"}, "overrides": []}, "gridPos": {"h": 5, "w": 24, "x": 0, "y": 16}, "id": 41, "options": {"colorMode": "none", "graphMode": "none", "justifyMode": "auto", "orientation": "auto", "reduceOptions": {"calcs": ["lastNotNull"], "fields": "", "values": false}, "textMode": "auto"}, "pluginVersion": "9.2.15", "targets": [{"datasource": {"type": "prometheus", "uid": "prometheus"}, "editorMode": "code", "expr": "max(kube_pod_container_resource_requests{namespace=~\"$namespace\",endpoint=\"http\",pod=~\"$pod\",container=~\"$container\",resource=\"memory\",unit=\"byte\",container!=\"POD\"})", "hide": false, "legendFormat": "Current requests", "range": true, "refId": "C"}, {"datasource": {"type": "prometheus", "uid": "prometheus"}, "editorMode": "code", "expr": "max(kube_pod_container_resource_limits{namespace=~\"$namespace\",endpoint=\"http\",pod=~\"$pod\",container=~\"$container\",resource=\"memory\",unit=\"byte\",container!=\"POD\"})", "hide": false, "legendFormat": "Current limits", "range": true, "refId": "D"}, {"datasource": {"type": "prometheus", "uid": "prometheus"}, "editorMode": "code", "expr": "max(kube_verticalpodautoscaler_status_recommendation_containerrecommendations_target{container=\"$service\", resource=\"memory\", namespace=\"$namespace\"})", "hide": false, "legendFormat": "Recommended requests", "range": true, "refId": "E"}, {"datasource": {"type": "prometheus", "uid": "prometheus"}, "editorMode": "code", "expr": "max(kube_verticalpodautoscaler_status_recommendation_containerrecommendations_upperbound{container=\"$service\", resource=\"memory\", namespace=\"$namespace\"})", "hide": false, "legendFormat": "Recommended limits", "range": true, "refId": "F"}], "title": "VPA - Memory Recommendations", "type": "stat"}, {"datasource": {"type": "prometheus", "uid": "prometheus"}, "description": "container_cpu_cfs_throttled_seconds_total is the Total time duration the container has been throttled in seconds. container_cpu_cfs_throttled_periods_total is the Number of throttled period intervals", "fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}, "custom": {"axisCenteredZero": false, "axisColorMode": "text", "axisLabel": "CPU (%)", "axisPlacement": "auto", "axisSoftMax": 100, "axisSoftMin": 0, "barAlignment": 0, "drawStyle": "line", "fillOpacity": 0, "gradientMode": "none", "hideFrom": {"legend": false, "tooltip": false, "viz": false}, "lineInterpolation": "linear", "lineStyle": {"fill": "solid"}, "lineWidth": 1, "pointSize": 5, "scaleDistribution": {"type": "linear"}, "showPoints": "auto", "spanNulls": false, "stacking": {"group": "A", "mode": "none"}, "thresholdsStyle": {"mode": "dashed"}}, "mappings": [], "thresholds": {"mode": "percentage", "steps": [{"color": "green"}, {"color": "#EAB839", "value": 70}, {"color": "red", "value": 90}]}, "unit": "none"}, "overrides": []}, "gridPos": {"h": 13, "w": 24, "x": 0, "y": 21}, "id": 27, "options": {"legend": {"calcs": ["max", "mean"], "displayMode": "table", "placement": "bottom", "showLegend": false}, "tooltip": {"mode": "multi", "sort": "none"}}, "targets": [{"datasource": {"type": "prometheus", "uid": "prometheus"}, "editorMode": "code", "expr": "sum by (namespace,pod) ((container_cpu_cfs_throttled_periods_total{pod=~\"$service.*\", namespace=\"$namespace\"} / container_cpu_cfs_periods_total{pod=~\"$service.*\", namespace=\"$namespace\"}) * 100)", "hide": false, "legendFormat": "{{pod}}", "range": true, "refId": "A"}], "title": "CPU throttling", "transparent": true, "type": "timeseries"}, {"datasource": {"type": "prometheus", "uid": "prometheus"}, "fieldConfig": {"defaults": {"color": {"mode": "thresholds"}, "custom": {"align": "auto", "displayMode": "auto", "filterable": false, "inspect": false}, "decimals": 0, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green"}, {"color": "red", "value": 80}]}, "unit": "none"}, "overrides": [{"matcher": {"id": "by<PERSON><PERSON>", "options": "{__name__=\"kube_pod_container_status_last_terminated_reason\", container=\"teleconsult-index-service\", endpoint=\"http\", instance=\"10.101.32.42:8080\", job=\"kube-state-metrics\", namespace=\"default\", pod=\"teleconsult-index-service-67548864bc-lxrxm\", reason=\"Error\", service=\"prometheus-operator-kube-state-metrics\", uid=\"2daf5193-e912-4a0a-a7eb-e230f772774a\"}"}, "properties": [{"id": "custom.width", "value": 232}]}, {"matcher": {"id": "by<PERSON><PERSON>", "options": "{__name__=\"kube_pod_container_status_last_terminated_reason\", container=\"teleconsult-index-service\", endpoint=\"http\", instance=\"10.101.32.42:8080\", job=\"kube-state-metrics\", namespace=\"default\", pod=\"teleconsult-index-service-554d58f9f4-zbcjd\", reason=\"Error\", service=\"prometheus-operator-kube-state-metrics\", uid=\"5eed78bb-0d93-4be1-9400-c2b4901b1ac1\"}"}, "properties": [{"id": "custom.width", "value": 244}]}, {"matcher": {"id": "by<PERSON><PERSON>", "options": "{__name__=\"kube_pod_container_status_last_terminated_reason\", container=\"teleconsult-index-service\", endpoint=\"http\", instance=\"10.101.32.42:8080\", job=\"kube-state-metrics\", namespace=\"default\", pod=\"teleconsult-index-service-77dd49cb5c-4f7nt\", reason=\"Error\", service=\"prometheus-operator-kube-state-metrics\", uid=\"cc64ebdd-e4a4-4e30-95c0-4e04cf86a196\"}"}, "properties": [{"id": "custom.width", "value": 292}]}, {"matcher": {"id": "by<PERSON><PERSON>", "options": "{container=\"teleconsult-index-service\", endpoint=\"http\", instance=\"10.101.32.42:8080\", job=\"kube-state-metrics\", namespace=\"default\", pod=\"teleconsult-index-service-554d58f9f4-zbcjd\", service=\"prometheus-operator-kube-state-metrics\", uid=\"5eed78bb-0d93-4be1-9400-c2b4901b1ac1\"}"}, "properties": [{"id": "custom.width", "value": 297}]}, {"matcher": {"id": "by<PERSON><PERSON>", "options": "endpoint"}, "properties": [{"id": "custom.width", "value": 84}]}, {"matcher": {"id": "by<PERSON><PERSON>", "options": "pod"}, "properties": [{"id": "custom.width", "value": 381}]}, {"matcher": {"id": "by<PERSON><PERSON>", "options": "container"}, "properties": [{"id": "custom.width", "value": 189}]}, {"matcher": {"id": "by<PERSON><PERSON>", "options": "namespace"}, "properties": [{"id": "custom.width", "value": 101}]}, {"matcher": {"id": "by<PERSON><PERSON>", "options": "reason"}, "properties": [{"id": "custom.width", "value": 84}]}]}, "gridPos": {"h": 10, "w": 13, "x": 0, "y": 34}, "id": 37, "options": {"footer": {"fields": "", "reducer": ["sum"], "show": false}, "showHeader": true, "sortBy": []}, "pluginVersion": "9.2.15", "targets": [{"datasource": {"type": "prometheus", "uid": "prometheus"}, "editorMode": "code", "exemplar": false, "expr": "group by (pod,namespace)(increase(kube_pod_container_status_restarts_total{namespace=~\"$namespace\",pod=~\"$pod\",container=~\"$container\"}[5m]))>0", "hide": true, "instant": false, "legendFormat": "__auto", "range": true, "refId": "A"}, {"datasource": {"type": "prometheus", "uid": "prometheus"}, "editorMode": "code", "expr": "sum (kube_pod_container_status_last_terminated_reason{namespace=~\"$namespace\",pod=~\"$pod\",container=~\"$container\"}) by (pod,namespace,container,reason)", "hide": true, "legendFormat": "__auto", "range": true, "refId": "B"}, {"datasource": {"type": "prometheus", "uid": "prometheus"}, "editorMode": "code", "exemplar": false, "expr": "kube_pod_container_status_last_terminated_reason{namespace=~\"$namespace\",pod=~\"$pod\",container=~\"$container\"}", "hide": false, "instant": false, "legendFormat": "__auto", "range": true, "refId": "C"}], "title": "Pod Termination ", "transformations": [{"id": "labelsToFields", "options": {"keepLabels": ["container", "namespace", "pod", "reason"], "mode": "columns"}}, {"id": "merge", "options": {}}, {"id": "organize", "options": {"excludeByName": {"Time": false, "Value": true}, "indexByName": {}, "renameByName": {}}}, {"id": "groupBy", "options": {"fields": {"Time": {"aggregations": ["lastNotNull"], "operation": "aggregate"}, "container": {"aggregations": [], "operation": "groupby"}, "namespace": {"aggregations": [], "operation": "groupby"}, "pod": {"aggregations": [], "operation": "groupby"}, "reason": {"aggregations": [], "operation": "groupby"}}}}], "type": "table"}, {"datasource": {"type": "prometheus", "uid": "prometheus"}, "fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}, "custom": {"axisCenteredZero": false, "axisColorMode": "text", "axisLabel": "", "axisPlacement": "auto", "barAlignment": 0, "drawStyle": "line", "fillOpacity": 0, "gradientMode": "none", "hideFrom": {"legend": false, "tooltip": false, "viz": false}, "lineInterpolation": "linear", "lineStyle": {"fill": "solid"}, "lineWidth": 1, "pointSize": 3, "scaleDistribution": {"type": "linear"}, "showPoints": "auto", "spanNulls": false, "stacking": {"group": "A", "mode": "none"}, "thresholdsStyle": {"mode": "off"}}, "mappings": [], "noValue": "No scaling information", "thresholds": {"mode": "absolute", "steps": [{"color": "green"}, {"color": "red", "value": 80}]}}, "overrides": []}, "gridPos": {"h": 10, "w": 11, "x": 13, "y": 34}, "id": 18, "options": {"legend": {"calcs": [], "displayMode": "list", "placement": "bottom", "showLegend": true}, "tooltip": {"mode": "single", "sort": "none"}}, "targets": [{"datasource": {"type": "prometheus", "uid": "prometheus"}, "editorMode": "code", "exemplar": false, "expr": "kube_horizontalpodautoscaler_spec_max_replicas{namespace=~\"$namespace\",horizontalpodautoscaler=~\"$service\"}", "format": "time_series", "hide": true, "instant": false, "interval": "", "legendFormat": "max-status-{{horizontalpodautoscaler}}", "range": true, "refId": "B"}, {"datasource": {"type": "prometheus", "uid": "prometheus"}, "editorMode": "code", "exemplar": false, "expr": "sum by (horizontalpodautoscaler)(kube_horizontalpodautoscaler_status_current_replicas{namespace=~\"$namespace\",horizontalpodautoscaler=~\"$service\"})", "format": "time_series", "hide": false, "instant": false, "interval": "", "legendFormat": "current-status-{{horizontalpodautoscaler}}", "range": true, "refId": "C"}], "title": "Pod Scaling information ", "type": "timeseries"}, {"datasource": {"type": "prometheus", "uid": "prometheus"}, "fieldConfig": {"defaults": {"color": {"mode": "thresholds"}, "custom": {"align": "left", "displayMode": "auto", "inspect": false}, "decimals": 0, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green"}, {"color": "red", "value": 80}]}, "unit": "none"}, "overrides": [{"matcher": {"id": "by<PERSON><PERSON>", "options": "namespace"}, "properties": [{"id": "custom.width", "value": 123}]}, {"matcher": {"id": "by<PERSON><PERSON>", "options": "Time"}, "properties": [{"id": "custom.width", "value": 210}]}, {"matcher": {"id": "by<PERSON><PERSON>", "options": "Pod "}, "properties": [{"id": "custom.width", "value": 377}]}, {"matcher": {"id": "by<PERSON><PERSON>", "options": "Timestamp"}, "properties": [{"id": "custom.width", "value": 162}]}, {"matcher": {"id": "by<PERSON><PERSON>", "options": "Count"}, "properties": [{"id": "custom.width", "value": 83}]}, {"matcher": {"id": "by<PERSON><PERSON>", "options": "Container"}, "properties": [{"id": "custom.width", "value": 244}]}]}, "gridPos": {"h": 9, "w": 13, "x": 0, "y": 44}, "id": 25, "options": {"footer": {"fields": "", "reducer": ["sum"], "show": false}, "showHeader": true, "sortBy": []}, "pluginVersion": "9.2.15", "targets": [{"datasource": {"type": "prometheus", "uid": "prometheus"}, "editorMode": "code", "exemplar": false, "expr": "round(increase(kube_pod_container_status_restarts_total{namespace=~\"$namespace\",pod=~\"$pod\",container=~\"$container\"}[5m]))>0", "legendFormat": "Pod: {{pod}}, Container:  {{container}}", "range": true, "refId": "A"}], "title": "Total Container Restarts ", "transformations": [{"id": "labelsToFields", "options": {"keepLabels": ["container", "namespace", "pod"]}}, {"id": "merge", "options": {}}, {"id": "organize", "options": {"excludeByName": {"Time": false, "Value": false}, "indexByName": {}, "renameByName": {"Time": "Timestamp", "Value": "Count", "container": "Container", "namespace": "Namespace", "pod": "Pod "}}}], "type": "table"}, {"datasource": {"type": "prometheus", "uid": "prometheus"}, "fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}, "custom": {"axisCenteredZero": false, "axisColorMode": "text", "axisLabel": "", "axisPlacement": "auto", "barAlignment": 0, "drawStyle": "line", "fillOpacity": 0, "gradientMode": "none", "hideFrom": {"legend": false, "tooltip": false, "viz": false}, "lineInterpolation": "linear", "lineWidth": 1, "pointSize": 5, "scaleDistribution": {"type": "linear"}, "showPoints": "auto", "spanNulls": false, "stacking": {"group": "A", "mode": "none"}, "thresholdsStyle": {"mode": "off"}}, "mappings": [], "noValue": "No data on non ready pods", "thresholds": {"mode": "absolute", "steps": [{"color": "green"}, {"color": "red", "value": 80}]}}, "overrides": []}, "gridPos": {"h": 9, "w": 11, "x": 13, "y": 44}, "id": 32, "options": {"legend": {"calcs": ["lastNotNull"], "displayMode": "table", "placement": "bottom", "showLegend": true}, "tooltip": {"mode": "single", "sort": "none"}}, "targets": [{"datasource": {"type": "prometheus", "uid": "prometheus"}, "editorMode": "code", "expr": "sum by (pod, container, reason, namespace) (kube_pod_container_status_last_terminated_reason{reason=\"OOMKilled\", container=~\"$container\",}) * on (pod,container) group_left sum by (pod, container) (changes(kube_pod_container_status_restarts_total{}[1m]))", "hide": true, "legendFormat": "__auto", "range": true, "refId": "A"}, {"datasource": {"type": "prometheus", "uid": "prometheus"}, "editorMode": "code", "expr": "increase(kube_pod_container_status_last_terminated_reason{namespace=~\"$namespace\",pod=~\"$pod\",container=~\"$container\"}[2m])", "hide": true, "legendFormat": "{{pod}}: {{reason}}", "range": true, "refId": "B"}, {"datasource": {"type": "prometheus", "uid": "prometheus"}, "editorMode": "code", "expr": "group(kube_pod_status_ready{namespace=~\"$namespace\",pod=~\"$pod\",condition!=\"true\"}>0) by (pod,condition)", "hide": false, "legendFormat": "Pod: {{pod}} condition: {{condition}}", "range": true, "refId": "C"}, {"datasource": {"type": "prometheus", "uid": "prometheus"}, "editorMode": "code", "expr": "group (kube_pod_status_reason{namespace=~\"$namespace\",pod=~\"$pod\"}>0) by (reason)", "hide": true, "legendFormat": "__auto", "range": true, "refId": "D"}], "title": "Non Ready pods ", "type": "timeseries"}, {"datasource": {"type": "prometheus", "uid": "prometheus"}, "description": "", "fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}, "custom": {"align": "auto", "displayMode": "auto", "filterable": true, "inspect": true}, "mappings": [], "noValue": "No Events", "thresholds": {"mode": "absolute", "steps": [{"color": "green"}, {"color": "red", "value": 80}]}}, "overrides": [{"matcher": {"id": "by<PERSON><PERSON>", "options": "Time"}, "properties": [{"id": "custom.width", "value": 159}]}, {"matcher": {"id": "by<PERSON><PERSON>", "options": "Value"}, "properties": [{"id": "custom.width", "value": 67}]}, {"matcher": {"id": "by<PERSON><PERSON>", "options": "app"}, "properties": [{"id": "custom.width", "value": 126}]}, {"matcher": {"id": "by<PERSON><PERSON>", "options": "involved_object_name"}, "properties": [{"id": "custom.width", "value": 320}]}, {"matcher": {"id": "by<PERSON><PERSON>", "options": "involved_object_kind"}, "properties": [{"id": "custom.width", "value": 175}]}, {"matcher": {"id": "by<PERSON><PERSON>", "options": "name"}, "properties": [{"id": "custom.width", "value": 336}]}, {"matcher": {"id": "by<PERSON><PERSON>", "options": "namespace"}, "properties": [{"id": "custom.width", "value": 95}]}, {"matcher": {"id": "by<PERSON><PERSON>", "options": "reason"}, "properties": [{"id": "custom.width", "value": 193}]}, {"matcher": {"id": "by<PERSON><PERSON>", "options": "source"}, "properties": [{"id": "custom.width", "value": 199}]}, {"matcher": {"id": "by<PERSON><PERSON>", "options": "message"}, "properties": [{"id": "custom.width", "value": 317}]}, {"matcher": {"id": "by<PERSON><PERSON>", "options": "kubernetes_pod_name"}, "properties": [{"id": "custom.width", "value": 232}]}, {"matcher": {"id": "by<PERSON><PERSON>", "options": "Namespace"}, "properties": [{"id": "custom.width", "value": 147}]}, {"matcher": {"id": "by<PERSON><PERSON>", "options": "Message"}, "properties": [{"id": "custom.width", "value": 558}]}]}, "gridPos": {"h": 20, "w": 24, "x": 0, "y": 53}, "id": 31, "options": {"footer": {"enablePagination": false, "fields": "", "reducer": ["sum"], "show": false}, "frameIndex": 61, "showHeader": true, "sortBy": []}, "pluginVersion": "9.2.15", "targets": [{"datasource": {"type": "prometheus", "uid": "prometheus"}, "editorMode": "code", "expr": "kube_event_exporter{involved_object_kind=~\"Endpoints|HorizontalPodAutoscaler|Pod|ReplicaSet|StatefulSet|Deployment\", involved_object_name=~\"$pod\"}", "hide": false, "legendFormat": "__auto", "range": true, "refId": "A"}], "title": "Application Events", "transformations": [{"id": "labelsToFields", "options": {"keepLabels": ["reason", "type", "involved_object_namespace", "kubernetes_namespace", "involved_object_kind", "involved_object_name", "message", "kubernetes_pod_name"], "mode": "columns"}}, {"id": "merge", "options": {}}, {"disabled": true, "id": "joinByLabels", "options": {"join": ["involved_object_kind", "involved_object_name", "namespace", "name", "reason", "source", "type", "message"], "value": "kubernetes_pod_name"}}, {"id": "organize", "options": {"excludeByName": {"Value": true, "involved_object_namespace": true, "kubernetes_pod_name": true}, "indexByName": {}, "renameByName": {"involved_object_kind": "Kind", "involved_object_name": "Name", "involved_object_namespace": "", "kubernetes_namespace": "Namespace", "message": "Message", "reason": "Reason ", "type": "Type"}}}], "type": "table"}], "schemaVersion": 37, "style": "dark", "tags": [], "templating": {"list": [{"allValue": ".*", "current": {"selected": false, "text": "default", "value": "default"}, "datasource": {"type": "prometheus", "uid": "prometheus"}, "definition": "label_values(kube_pod_info,namespace)", "hide": 0, "includeAll": true, "multi": false, "name": "namespace", "options": [], "query": {"query": "label_values(kube_pod_info,namespace)", "refId": "StandardVariableQuery"}, "refresh": 1, "regex": "", "skipUrlSync": false, "sort": 0, "type": "query"}, {"allValue": ".*", "current": {"selected": true, "text": "All", "value": "$__all"}, "datasource": {"type": "prometheus", "uid": "prometheus"}, "definition": "label_values(kube_pod_labels{namespace=\"$namespace\"},label_app)", "hide": 0, "includeAll": true, "multi": false, "name": "service", "options": [], "query": {"query": "label_values(kube_pod_labels{namespace=\"$namespace\"},label_app)", "refId": "StandardVariableQuery"}, "refresh": 1, "regex": "", "skipUrlSync": false, "sort": 0, "type": "query"}, {"allValue": "$service.*", "current": {"selected": true, "text": ["All"], "value": ["$__all"]}, "datasource": {"type": "prometheus", "uid": "prometheus"}, "definition": "label_values(kube_pod_labels{namespace=\"$namespace\",label_app=~\"$service\"},pod)", "hide": 0, "includeAll": true, "multi": true, "name": "pod", "options": [], "query": {"query": "label_values(kube_pod_labels{namespace=\"$namespace\",label_app=~\"$service\"},pod)", "refId": "StandardVariableQuery"}, "refresh": 1, "regex": "", "skipUrlSync": false, "sort": 0, "type": "query"}, {"allValue": ".*", "current": {"selected": true, "text": ["All"], "value": ["$__all"]}, "datasource": {"type": "prometheus", "uid": "prometheus"}, "definition": "label_values(kube_pod_container_info{namespace=~\"$namespace\",pod=~\"$pod\"},container)", "hide": 0, "includeAll": true, "multi": true, "name": "container", "options": [], "query": {"query": "label_values(kube_pod_container_info{namespace=~\"$namespace\",pod=~\"$pod\"},container)", "refId": "StandardVariableQuery"}, "refresh": 1, "regex": "", "skipUrlSync": false, "sort": 0, "type": "query"}]}, "time": {"from": "now-15d", "to": "now"}, "timepicker": {}, "timezone": "", "title": "Application dashboard [New]", "uid": "x7Q57j9xA4X11hfW", "version": 1, "weekStart": ""}}, "advanced": {"inherit_from_base": true}}