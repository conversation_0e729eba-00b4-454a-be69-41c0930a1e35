intent: "cloud_account"
alias-flavors: ["default"]
flavor: aws
lifecycle: ENVIRONMENT
version: "0.1"
depends_on: []
input_type: instance
composition: {}
conditional_on_intent: ""
clouds:
- aws
spec: {}
outputs:
  default:
    type: "@outputs/cloud_account"
    providers:
      aws:
        source: hashicorp/aws
        version: "= 3.74.0"
        attributes:
          region: attributes.aws_region
          skip_region_validation: true
          attributes:
            assume_role:
              role_arn: attributes.aws_iam_role
              session_name: attributes.session_name
              external_id: attributes.external_id
      aws4:
        source: hashicorp/aws4
        version: "= 4.53.0"
        attributes:
          region: attributes.aws_region
          skip_region_validation: true
          attributes:
            assume_role:
              role_arn: attributes.aws_iam_role
              session_name: attributes.session_name
              external_id: attributes.external_id
      aws5:
        source: hashicorp/aws5
        version: "= 5.49.0"
        attributes:
          region: attributes.aws_region
          skip_region_validation: true
          attributes:
            assume_role:
              role_arn: attributes.aws_iam_role
              session_name: attributes.session_name
              external_id: attributes.external_id
sample:
  version: "0.1"
  flavor: aws
  kind: cloud_account
  disabled: false
  spec: {}
