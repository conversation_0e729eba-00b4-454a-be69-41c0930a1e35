intent: image_pull_secret_injector
flavor: default
version: "0.1"
description: Adds image pull secrets injector - default flavor
clouds:
  - aws
  - gcp
  - azure
  - kubernetes
lifecycle: ENVIRONMENT
input_type: instance
composition: {}
inputs:
  kubernetes_details:
    type: "@output/kubernetes"
    displayName: Kubernetes Cluster
    description: The details of kubernetes cluster where the image pull secret injector will be installed
    optional: false
    default:
      resource_type: kubernetes_cluster
      resource_name: default
  artifactories:
    type: "@output/artifactories"
    displayName: Artifactories
    description: The details of container registries associated with this Artifactory.
    optional: false
    default:
      resource_type: artifactories
      resource_name: default
spec:
  title: Image Pull Secret Injector Spec
  description: Specifications for Image Pull Secret Injector
  type: object
  properties:
    size:
      type: object
      title: Size
      description: Sizing for Image Pull Secret Injector
      properties:
        cpu_limit:
          type: string
          title: CPU Limit
          description: CPU limit for the instance
          minLength: 1
          pattern: "^([1-9]|[12][0-9]|3[0-2])$|^(3[0-1][0-9]{3}|[1-2][0-9]{4}|[1-9][0-9]{0,3}|32000)m$"
          x-ui-placeholder: "Enter CPU requests for your application"
          x-ui-error-message: "Value doesn't match pattern, it should be number ranging from 1 to 32 or 1m to 32000m"
        memory_limit:
          type: string
          title: Memory Limit
          description: Memory limit for the instance
          minLength: 1
          pattern: "^(0\\.[1-9]|[1-9](\\.[0-9]+)?|[1-5][0-9](\\.[0-9]+)?|6[0-4])Gi$|^([1-9](\\.[0-9]+)?|[1-9][0-9]{1,3}(\\.[0-9]+)?|[1-5][0-9]{4}(\\.[0-9]+)?|6[0-3][0-9]{3}(\\.[0-9]+)?|64000)Mi$"
          x-ui-placeholder: "Enter Memory requests for your application"
          x-ui-error-message: "Value doesn't match pattern, it should be number ranging from 1Gi to 64Gi or 1Mi to 64000Mi"
    values:
      type: object
      title: Values
      description: The values to be passed on to the chart in the form of a YAML object
      x-ui-yaml-editor: true
      x-ui-placeholder: Enter values in YAML format
sample:
  kind: image_pull_secret_injector
  flavor: default
  version: "0.1"
  metadata: {}
  disabled: true
  spec:
    size:
      cpu_limit: 200m
      memory_limit: 200Mi
    values: {}
  advanced:
    inherit_from_base: true
