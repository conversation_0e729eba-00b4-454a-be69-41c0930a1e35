{"16Cores": {"onDemand": [{"Architecture": "x86_64", "BestOnDemandHourPriceRegion": "us-central1", "CanonicalName": "e2-highcpu-16", "InstanceType": "e2-highcpu-16", "Memory": 16384, "ModifiedDate": "2024-11-13 00:01:25", "PaymentType": "OnDemand", "PricePerHour": 0.55375536, "tags": ["cheapest"]}, {"Architecture": "x86_64", "BestOnDemandHourPriceRegion": "asia-south1", "CanonicalName": "n2d-highcpu-16", "InstanceType": "n2d-highcpu-16", "Memory": 16384, "ModifiedDate": "2024-11-13 00:01:25", "PaymentType": "OnDemand", "PricePerHour": 0.698064, "tags": []}, {"Architecture": "x86_64", "BestOnDemandHourPriceRegion": "us-central1", "CanonicalName": "e2-standard-16", "InstanceType": "e2-standard-16", "Memory": 65536, "ModifiedDate": "2024-11-13 00:01:25", "PaymentType": "OnDemand", "PricePerHour": 0.75009072, "tags": []}, {"Architecture": "x86_64", "BestOnDemandHourPriceRegion": "us-central1", "CanonicalName": "n1-highcpu-16", "InstanceType": "n1-highcpu-16", "Memory": 14746, "ModifiedDate": "2024-11-13 00:01:25", "PaymentType": "OnDemand", "PricePerHour": 0.793061515625, "tags": []}], "spot": [{"Architecture": "x86_64", "BestOnDemandHourPriceRegion": "asia-south1", "CanonicalName": "n2d-highcpu-16", "InstanceType": "n2d-highcpu-16", "Memory": 16384, "ModifiedDate": "2024-11-13 00:01:25", "PaymentType": "OnDemand", "PricePerHour": 0.160576, "tags": ["cheapest"]}, {"Architecture": "x86_64", "BestOnDemandHourPriceRegion": "us-central1", "CanonicalName": "n1-highcpu-16", "InstanceType": "n1-highcpu-16", "Memory": 14746, "ModifiedDate": "2024-11-13 00:01:25", "PaymentType": "OnDemand", "PricePerHour": 0.17528531445312498, "tags": []}, {"Architecture": "x86_64", "BestOnDemandHourPriceRegion": "asia-south1", "CanonicalName": "t2d-standard-16", "InstanceType": "t2d-standard-16", "Memory": 65536, "ModifiedDate": "2024-11-13 00:01:25", "PaymentType": "OnDemand", "PricePerHour": 0.185856, "tags": []}, {"Architecture": "x86_64", "BestOnDemandHourPriceRegion": "us-central1", "CanonicalName": "n2-highcpu-16", "InstanceType": "n2-highcpu-16", "Memory": 16384, "ModifiedDate": "2024-11-13 00:01:25", "PaymentType": "OnDemand", "PricePerHour": 0.19992000000000001, "tags": []}]}, "4Cores": {"onDemand": [{"Architecture": "x86_64", "BestOnDemandHourPriceRegion": "us-central1", "CanonicalName": "e2-highcpu-4", "InstanceType": "e2-highcpu-4", "Memory": 4096, "ModifiedDate": "2024-11-13 00:01:25", "PaymentType": "OnDemand", "PricePerHour": 0.13843884, "tags": ["cheapest"]}, {"Architecture": "x86_64", "BestOnDemandHourPriceRegion": "asia-south1", "CanonicalName": "n2d-highcpu-4", "InstanceType": "n2d-highcpu-4", "Memory": 4096, "ModifiedDate": "2024-11-13 00:01:25", "PaymentType": "OnDemand", "PricePerHour": 0.174516, "tags": []}, {"Architecture": "x86_64", "BestOnDemandHourPriceRegion": "us-central1", "CanonicalName": "e2-standard-4", "InstanceType": "e2-standard-4", "Memory": 16384, "ModifiedDate": "2024-11-13 00:01:25", "PaymentType": "OnDemand", "PricePerHour": 0.18752268, "tags": []}, {"Architecture": "x86_64", "BestOnDemandHourPriceRegion": "us-central1", "CanonicalName": "n1-highcpu-4", "InstanceType": "n1-highcpu-4", "Memory": 3686, "ModifiedDate": "2024-11-13 00:01:25", "PaymentType": "OnDemand", "PricePerHour": 0.198262484375, "tags": []}], "spot": [{"Architecture": "x86_64", "BestOnDemandHourPriceRegion": "asia-south1", "CanonicalName": "n2d-highcpu-4", "InstanceType": "n2d-highcpu-4", "Memory": 4096, "ModifiedDate": "2024-11-13 00:01:25", "PaymentType": "OnDemand", "PricePerHour": 0.040144, "tags": ["cheapest"]}, {"Architecture": "x86_64", "BestOnDemandHourPriceRegion": "us-central1", "CanonicalName": "n1-highcpu-4", "InstanceType": "n1-highcpu-4", "Memory": 3686, "ModifiedDate": "2024-11-13 00:01:25", "PaymentType": "OnDemand", "PricePerHour": 0.043820685546874995, "tags": []}, {"Architecture": "x86_64", "BestOnDemandHourPriceRegion": "asia-south1", "CanonicalName": "t2d-standard-4", "InstanceType": "t2d-standard-4", "Memory": 16384, "ModifiedDate": "2024-11-13 00:01:25", "PaymentType": "OnDemand", "PricePerHour": 0.046464, "tags": []}, {"Architecture": "x86_64", "BestOnDemandHourPriceRegion": "us-central1", "CanonicalName": "n2-highcpu-4", "InstanceType": "n2-highcpu-4", "Memory": 4096, "ModifiedDate": "2024-11-13 00:01:25", "PaymentType": "OnDemand", "PricePerHour": 0.049980000000000004, "tags": []}]}, "8Cores": {"onDemand": [{"Architecture": "x86_64", "BestOnDemandHourPriceRegion": "us-central1", "CanonicalName": "e2-highcpu-8", "InstanceType": "e2-highcpu-8", "Memory": 8192, "ModifiedDate": "2024-11-13 00:01:25", "PaymentType": "OnDemand", "PricePerHour": 0.27687768, "tags": ["cheapest"]}, {"Architecture": "x86_64", "BestOnDemandHourPriceRegion": "asia-south1", "CanonicalName": "n2d-highcpu-8", "InstanceType": "n2d-highcpu-8", "Memory": 8192, "ModifiedDate": "2024-11-13 00:01:25", "PaymentType": "OnDemand", "PricePerHour": 0.349032, "tags": []}, {"Architecture": "x86_64", "BestOnDemandHourPriceRegion": "us-central1", "CanonicalName": "e2-standard-8", "InstanceType": "e2-standard-8", "Memory": 32768, "ModifiedDate": "2024-11-13 00:01:25", "PaymentType": "OnDemand", "PricePerHour": 0.37504536, "tags": []}, {"Architecture": "x86_64", "BestOnDemandHourPriceRegion": "us-central1", "CanonicalName": "n1-highcpu-8", "InstanceType": "n1-highcpu-8", "Memory": 7373, "ModifiedDate": "2024-11-13 00:01:25", "PaymentType": "OnDemand", "PricePerHour": 0.3965307578125, "tags": []}], "spot": [{"Architecture": "x86_64", "BestOnDemandHourPriceRegion": "asia-south1", "CanonicalName": "n2d-highcpu-8", "InstanceType": "n2d-highcpu-8", "Memory": 8192, "ModifiedDate": "2024-11-13 00:01:25", "PaymentType": "OnDemand", "PricePerHour": 0.080288, "tags": ["cheapest"]}, {"Architecture": "x86_64", "BestOnDemandHourPriceRegion": "us-central1", "CanonicalName": "n1-highcpu-8", "InstanceType": "n1-highcpu-8", "Memory": 7373, "ModifiedDate": "2024-11-13 00:01:25", "PaymentType": "OnDemand", "PricePerHour": 0.08764265722656249, "tags": []}, {"Architecture": "x86_64", "BestOnDemandHourPriceRegion": "asia-south1", "CanonicalName": "t2d-standard-8", "InstanceType": "t2d-standard-8", "Memory": 32768, "ModifiedDate": "2024-11-13 00:01:25", "PaymentType": "OnDemand", "PricePerHour": 0.092928, "tags": []}, {"Architecture": "x86_64", "BestOnDemandHourPriceRegion": "us-central1", "CanonicalName": "n2-highcpu-8", "InstanceType": "n2-highcpu-8", "Memory": 8192, "ModifiedDate": "2024-11-13 00:01:25", "PaymentType": "OnDemand", "PricePerHour": 0.09996000000000001, "tags": []}]}}