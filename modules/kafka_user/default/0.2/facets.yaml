intent: kafka_user
flavor: default
version: '0.2'
description: Adds kafka_user - default flavor
clouds:
  - aws
  - gcp
  - azure
  - kubernetes
lifecycle: ENVIRONMENT
input_type: instance
inputs:
  kafka_details:
    type: "@output/kafka"
    displayName: Kafka
    description: The details of Kafka where the user needs to be created
    optional: false
spec:
  title: Kafka User
  type: object
  description: Specifications of user for Kafka
sample:
  kind: kafka_user
  flavor: default
  version: '0.2'
  metadata: { }
  spec: { }
  disabled: true
