{"kind": "grafana_dashboard", "flavor": "default", "version": "latest", "lifecycle": "ENVIRONMENT", "disabled": false, "conditional_on_intent": "log_collector", "provided": false, "depends_on": [], "metadata": {"name": "quick-log-search"}, "advanced": {"inherit_from_base": true}, "spec": {"dashboard": {"annotations": {"list": [{"builtIn": 1, "datasource": "-- <PERSON><PERSON> --", "enable": true, "hide": true, "iconColor": "rgba(0, 211, 255, 1)", "name": "Annotations & Alerts", "type": "dashboard"}]}, "description": "Loki logs panel with loki variables ", "editable": true, "gnetId": 12019, "graphTooltip": 0, "id": 21, "iteration": 1665738745432, "links": [], "panels": [{"aliasColors": {}, "bars": true, "dashLength": 10, "dashes": false, "datasource": "$loki_datasource", "fill": 1, "fillGradient": 0, "gridPos": {"h": 3, "w": 24, "x": 0, "y": 0}, "hiddenSeries": false, "id": 6, "legend": {"avg": false, "current": false, "max": false, "min": false, "show": false, "total": false, "values": false}, "lines": false, "linewidth": 1, "nullPointMode": "null", "options": {"dataLinks": []}, "percentage": false, "pointradius": 2, "points": false, "renderer": "flot", "seriesOverrides": [], "spaceLength": 10, "stack": false, "steppedLine": false, "targets": [{"expr": "sum(count_over_time({namespace=\"$namespace\", app=~\"$app\"} |~ \"$search\"[$__interval]))", "refId": "A"}], "thresholds": [], "timeFrom": null, "timeRegions": [], "timeShift": null, "title": "", "tooltip": {"shared": true, "sort": 0, "value_type": "individual"}, "type": "graph", "xaxis": {"buckets": null, "mode": "time", "name": null, "show": true, "values": []}, "yaxes": [{"format": "short", "label": null, "logBase": 1, "max": null, "min": null, "show": false}, {"format": "short", "label": null, "logBase": 1, "max": null, "min": null, "show": false}], "yaxis": {"align": false, "alignLevel": null}}, {"datasource": "$loki_datasource", "gridPos": {"h": 25, "w": 24, "x": 0, "y": 3}, "id": 2, "maxDataPoints": "", "options": {"showLabels": false, "showTime": true, "sortOrder": "Descending", "wrapLogMessage": true}, "targets": [{"expr": "{namespace=\"$namespace\", app=~\"$app\"} |~ \"$search\"", "refId": "A"}], "timeFrom": null, "timeShift": null, "title": "Logs Panel", "type": "logs"}, {"content": "<div style=\"text-align:center\"> For Grafana Loki blog example </div>\n\n\n", "datasource": null, "gridPos": {"h": 3, "w": 24, "x": 0, "y": 28}, "id": 4, "mode": "html", "timeFrom": null, "timeShift": null, "title": "", "transparent": true, "type": "text"}], "refresh": "30s", "schemaVersion": 22, "style": "dark", "tags": [], "templating": {"list": [{"hide": 0, "label": null, "name": "loki_datasource", "options": [], "query": "loki", "refresh": 1, "regex": "", "type": "datasource"}, {"allValue": null, "current": {"text": "loki", "value": "loki"}, "datasource": "$loki_datasource", "definition": "Namespace", "hide": 0, "includeAll": false, "index": -1, "label": null, "multi": false, "name": "namespace", "options": [], "query": {"label": "namespace"}, "refresh": 2, "regex": "", "skipUrlSync": false, "sort": 0, "tagValuesQuery": "", "tags": [], "tagsQuery": "", "type": "query", "useTags": false}, {"allValue": ".*", "current": {"text": "All", "value": ["$__all"]}, "datasource": "$loki_datasource", "definition": "App", "hide": 0, "includeAll": true, "index": -1, "label": null, "multi": true, "name": "app", "options": [], "query": {"label": "app"}, "refresh": 1, "regex": "", "skipUrlSync": false, "sort": 0, "tagValuesQuery": "", "tags": [], "tagsQuery": "", "type": "query", "useTags": false}, {"current": {"selected": false, "text": "", "value": ""}, "hide": 0, "label": null, "name": "search", "options": [{"selected": false, "text": "", "value": ""}], "query": "", "skipUrlSync": false, "type": "textbox"}]}, "time": {"from": "now-1h", "to": "now"}, "timepicker": {"refresh_intervals": ["5s", "10s", "30s", "1m", "5m", "15m", "30m", "1h", "2h", "1d"]}, "timezone": "", "title": "Quick Log Search", "uid": "liz0yRCZRas", "variables": {"list": []}, "version": 1}}}