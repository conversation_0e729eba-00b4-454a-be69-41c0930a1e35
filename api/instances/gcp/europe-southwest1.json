{"16Cores": {"onDemand": [{"Architecture": "x86_64", "BestOnDemandHourPriceRegion": "us-central1", "CanonicalName": "e2-highcpu-16", "InstanceType": "e2-highcpu-16", "Memory": 16384, "ModifiedDate": "2024-11-13 00:01:25", "PaymentType": "OnDemand", "PricePerHour": 0.467008, "tags": ["cheapest"]}, {"Architecture": "x86_64", "BestOnDemandHourPriceRegion": "asia-south1", "CanonicalName": "n2d-highcpu-16", "InstanceType": "n2d-highcpu-16", "Memory": 16384, "ModifiedDate": "2024-11-13 00:01:25", "PaymentType": "OnDemand", "PricePerHour": 0.58882944, "tags": []}, {"Architecture": "x86_64", "BestOnDemandHourPriceRegion": "us-central1", "CanonicalName": "e2-standard-16", "InstanceType": "e2-standard-16", "Memory": 65536, "ModifiedDate": "2024-11-13 00:01:25", "PaymentType": "OnDemand", "PricePerHour": 0.6326080000000001, "tags": []}, {"Architecture": "x86_64", "BestOnDemandHourPriceRegion": "us-central1", "CanonicalName": "n2-highcpu-16", "InstanceType": "n2-highcpu-16", "Memory": 16384, "ModifiedDate": "2024-11-13 00:01:25", "PaymentType": "OnDemand", "PricePerHour": 0.67681024, "tags": []}], "spot": [{"Architecture": "x86_64", "BestOnDemandHourPriceRegion": "asia-south1", "CanonicalName": "t2d-standard-16", "InstanceType": "t2d-standard-16", "Memory": 65536, "ModifiedDate": "2024-11-13 00:01:25", "PaymentType": "OnDemand", "PricePerHour": 0.081536, "tags": ["cheapest"]}, {"Architecture": "x86_64", "BestOnDemandHourPriceRegion": "us-central1", "CanonicalName": "n2-highcpu-16", "InstanceType": "n2-highcpu-16", "Memory": 16384, "ModifiedDate": "2024-11-13 00:01:25", "PaymentType": "OnDemand", "PricePerHour": 0.118672, "tags": []}, {"Architecture": "x86_64", "BestOnDemandHourPriceRegion": "asia-south1", "CanonicalName": "n2d-highcpu-16", "InstanceType": "n2d-highcpu-16", "Memory": 16384, "ModifiedDate": "2024-11-13 00:01:25", "PaymentType": "OnDemand", "PricePerHour": 0.14865599999999998, "tags": []}, {"Architecture": "x86_64", "BestOnDemandHourPriceRegion": "us-central1", "CanonicalName": "n2-standard-16", "InstanceType": "n2-standard-16", "Memory": 65536, "ModifiedDate": "2024-11-13 00:01:25", "PaymentType": "OnDemand", "PricePerHour": 0.160768, "tags": []}]}, "4Cores": {"onDemand": [{"Architecture": "x86_64", "BestOnDemandHourPriceRegion": "us-central1", "CanonicalName": "e2-highcpu-4", "InstanceType": "e2-highcpu-4", "Memory": 4096, "ModifiedDate": "2024-11-13 00:01:25", "PaymentType": "OnDemand", "PricePerHour": 0.116752, "tags": ["cheapest"]}, {"Architecture": "x86_64", "BestOnDemandHourPriceRegion": "asia-south1", "CanonicalName": "n2d-highcpu-4", "InstanceType": "n2d-highcpu-4", "Memory": 4096, "ModifiedDate": "2024-11-13 00:01:25", "PaymentType": "OnDemand", "PricePerHour": 0.14720736, "tags": []}, {"Architecture": "x86_64", "BestOnDemandHourPriceRegion": "us-central1", "CanonicalName": "e2-standard-4", "InstanceType": "e2-standard-4", "Memory": 16384, "ModifiedDate": "2024-11-13 00:01:25", "PaymentType": "OnDemand", "PricePerHour": 0.15815200000000001, "tags": []}, {"Architecture": "x86_64", "BestOnDemandHourPriceRegion": "us-central1", "CanonicalName": "n2-highcpu-4", "InstanceType": "n2-highcpu-4", "Memory": 4096, "ModifiedDate": "2024-11-13 00:01:25", "PaymentType": "OnDemand", "PricePerHour": 0.16920256, "tags": []}], "spot": [{"Architecture": "x86_64", "BestOnDemandHourPriceRegion": "asia-south1", "CanonicalName": "t2d-standard-4", "InstanceType": "t2d-standard-4", "Memory": 16384, "ModifiedDate": "2024-11-13 00:01:25", "PaymentType": "OnDemand", "PricePerHour": 0.020384, "tags": ["cheapest"]}, {"Architecture": "x86_64", "BestOnDemandHourPriceRegion": "us-central1", "CanonicalName": "n2-highcpu-4", "InstanceType": "n2-highcpu-4", "Memory": 4096, "ModifiedDate": "2024-11-13 00:01:25", "PaymentType": "OnDemand", "PricePerHour": 0.029668, "tags": []}, {"Architecture": "x86_64", "BestOnDemandHourPriceRegion": "asia-south1", "CanonicalName": "n2d-highcpu-4", "InstanceType": "n2d-highcpu-4", "Memory": 4096, "ModifiedDate": "2024-11-13 00:01:25", "PaymentType": "OnDemand", "PricePerHour": 0.037163999999999996, "tags": []}, {"Architecture": "x86_64", "BestOnDemandHourPriceRegion": "us-central1", "CanonicalName": "n2-standard-4", "InstanceType": "n2-standard-4", "Memory": 16384, "ModifiedDate": "2024-11-13 00:01:25", "PaymentType": "OnDemand", "PricePerHour": 0.040192, "tags": []}]}, "8Cores": {"onDemand": [{"Architecture": "x86_64", "BestOnDemandHourPriceRegion": "us-central1", "CanonicalName": "e2-highcpu-8", "InstanceType": "e2-highcpu-8", "Memory": 8192, "ModifiedDate": "2024-11-13 00:01:25", "PaymentType": "OnDemand", "PricePerHour": 0.233504, "tags": ["cheapest"]}, {"Architecture": "x86_64", "BestOnDemandHourPriceRegion": "asia-south1", "CanonicalName": "n2d-highcpu-8", "InstanceType": "n2d-highcpu-8", "Memory": 8192, "ModifiedDate": "2024-11-13 00:01:25", "PaymentType": "OnDemand", "PricePerHour": 0.29441472, "tags": []}, {"Architecture": "x86_64", "BestOnDemandHourPriceRegion": "us-central1", "CanonicalName": "e2-standard-8", "InstanceType": "e2-standard-8", "Memory": 32768, "ModifiedDate": "2024-11-13 00:01:25", "PaymentType": "OnDemand", "PricePerHour": 0.31630400000000003, "tags": []}, {"Architecture": "x86_64", "BestOnDemandHourPriceRegion": "us-central1", "CanonicalName": "n2-highcpu-8", "InstanceType": "n2-highcpu-8", "Memory": 8192, "ModifiedDate": "2024-11-13 00:01:25", "PaymentType": "OnDemand", "PricePerHour": 0.33840512, "tags": []}], "spot": [{"Architecture": "x86_64", "BestOnDemandHourPriceRegion": "asia-south1", "CanonicalName": "t2d-standard-8", "InstanceType": "t2d-standard-8", "Memory": 32768, "ModifiedDate": "2024-11-13 00:01:25", "PaymentType": "OnDemand", "PricePerHour": 0.040768, "tags": ["cheapest"]}, {"Architecture": "x86_64", "BestOnDemandHourPriceRegion": "us-central1", "CanonicalName": "n2-highcpu-8", "InstanceType": "n2-highcpu-8", "Memory": 8192, "ModifiedDate": "2024-11-13 00:01:25", "PaymentType": "OnDemand", "PricePerHour": 0.059336, "tags": []}, {"Architecture": "x86_64", "BestOnDemandHourPriceRegion": "asia-south1", "CanonicalName": "n2d-highcpu-8", "InstanceType": "n2d-highcpu-8", "Memory": 8192, "ModifiedDate": "2024-11-13 00:01:25", "PaymentType": "OnDemand", "PricePerHour": 0.07432799999999999, "tags": []}, {"Architecture": "x86_64", "BestOnDemandHourPriceRegion": "us-central1", "CanonicalName": "n2-standard-8", "InstanceType": "n2-standard-8", "Memory": 32768, "ModifiedDate": "2024-11-13 00:01:25", "PaymentType": "OnDemand", "PricePerHour": 0.080384, "tags": []}]}}