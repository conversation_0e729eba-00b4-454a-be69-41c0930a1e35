intent: kubernetes_node_pool
flavor: eks_managed
version: "0.2"
description: Adds kubernetes_node_pool - eks_managed flavor
clouds:
  - aws
spec:
  title: Kubernetes Node Pool
  description: Specification of the kubernetes node pool for eks managed flavor
  type: object
  properties:
    instance_type:
      title: Instance Type
      description: Comma-separated list of instance types for nodes in node pool
      type: string
      x-ui-placeholder: e.g. 'm4.xlarge,t3.2xlarge'
    min_node_count:
      title: Min Node Count
      description: Minimum number of nodes which should exist within this node pool
      type: number
      minimum: 0
      maximum: 100
    max_node_count:
      title: Max Node Count
      description: Maximum number of nodes which should exist within this node pool
      type: number
      minimum: 0
      maximum: 100
    is_public:
      title: Is Public
      description: Set this to true to deploy the node pool in public subnets
      type: boolean
    disk_size:
      title: Disk Size
      description: Size of the Disk in GiB for node in this node pool
      type: number
      minimum: 50
      maximum: 1000
    azs:
      title: Avalability Zones
      description: Comma-separated list of availability zones where worker nodes should be deployed
      type: string
    taints:
      title: Taints
      description: Map of Kubernetes taints which should be applied to nodes in the node pool
      type: object
      patternProperties:
        type: object
        title: Taint Object
        keyPattern: "^[a-z][a-z0-9-_]*$"
        properties:
          key:
            title: Taint Key
            description: Taint Key
            type: string
            x-ui-placeholder: CriticalAddonsOnly
          value:
            title: Taint Value
            description: Taint Value
            type: string
            x-ui-placeholder: "true"
          effect:
            title: Taint Effect
            description: Taint Effect
            type: string
            x-ui-placeholder: NoSchedule
    labels:
      title: Labels
      description: "Map of labels to be added to nodes in node pool. Enter key-value pair for labels in YAML format. Eg. key: value (provide a space after ':' as expected in the YAML format)"
      x-ui-placeholder: "Eg. key1: value1"
      type: object
      x-ui-yaml-editor: true
    capacity_type:
      title: Capacity Type
      description: Lifecycle plan for worker node
      type: string
      enum:
        - ON_DEMAND
        - SPOT
    ami_id:
      title: AMI ID
      description: AMI ID of the AMI image to be used in for the kubernetes node. This should be self owned ami.
      type: string
    ami_name_filter:
      title: AMI Name Filter
      description: AMI name filter for AMI image to be used for the kubernetes node.
      type: string
    ami_owner_id:
      title: AMI Owner ID
      description: AMI owner id of the AMI image to be used for the kubernetes node while using a name filter.
      type: string
  required:
    - instance_type
    - min_node_count
    - max_node_count
    - disk_size
    - taints
    - labels
sample:
  $schema: https://facets-cloud.github.io/facets-schemas/schemas/kubernetes_node_pool/kubernetes_node_pool.schema.json
  kind: kubernetes_node_pool
  flavor: eks_managed
  version: "0.2"
  metadata:
    tags:
      managed-by: facets
    annotations: {}
  spec:
    instance_type: t3.medium
    min_node_count: 1
    max_node_count: 5
    is_public: false
    disk_size: 50
    azs: us-east-1a
    taints:
      taint1:
        key: CriticalAddonsOnly
        value: "true"
        effect: NoSchedule
      taint2:
        effect: PreferNoSchedule
        key: special
        value: "true"
    labels:
      MyNodeType: MyAppNodes
  disabled: true
