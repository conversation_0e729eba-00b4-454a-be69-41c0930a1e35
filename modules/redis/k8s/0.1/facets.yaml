intent: redis
flavor: k8s
version: "0.1"
description: Adds redis module of flavor kubernetes
clouds:
  - aws
  - azure
  - gcp
  - kubernetes
metadata:
  title: Metadata of Mongo
  type: object
  properties:
    namespace:
      type: string
      title: Namespace 
      description: Namespace in which Mongo should be deployed
      default: default
spec:
  title: K8s
  type: object
  properties:
    authenticated:
      type: boolean
      title: Authenticated
      description: Enables password protection.
    persistence_enabled:
      type: boolean
      title: Persistence Enabled
      description: Ensures data is preserved across restarts.
    redis_version:
      type: string
      title: Redis Version
      description: Specifies the Redis version to use.
      enum:
        - "3.2.9-r3"
        - "4.0.14"
        - "5.0.14"
        - "6.2"
        - "6.2.16"
        - "7.0"
        - "7.4.1"
    size:
      type: object
      title: Size
      description: Configuration for datastore components.
      x-ui-order:
        - writer
        - reader
      required:
        - writer
      properties:
        reader:
          type: object
          title: Reader
          description: Settings for reader instances.
          properties:
            instance_count:
              type: integer
              title: Instance Count
              description: Number of reader instances.
              minimum: 0
              maximum: 10
              x-ui-error-message: "Instance count must be a non-negative integer not exceeding 10."
            cpu:
              type: string
              title: CPU
              description: Number of CPU cores required.
              pattern: "^([1-9]|[12][0-9]|3[0-2])$|^(3[0-1][0-9]{3}|[1-2][0-9]{4}|[1-9][0-9]{0,3}|32000)m$"
              x-ui-compare: 
                field: spec.size.reader.cpu_limit
                comparator: '<='
                x-ui-error-message: 'CPU cannot be more than CPU limit'
              x-ui-placeholder: "e.g., '500m' or '1'"
              x-ui-error-message: "Value doesn't match pattern, it should be number ranging from 1 to 32 or 1m to 32000m"
            memory:
              type: string
              title: Memory
              description: Amount of memory required.
              x-ui-compare: 
                field: spec.size.reader.memory_limit
                comparator: '<='
                x-ui-error-message: 'Memory cannot be more than memory limit'
              pattern: "^(0\\.[1-9]|[1-9](\\.[0-9]+)?|[1-5][0-9](\\.[0-9]+)?|6[0-4])G$|^([1-9](\\.[0-9]+)?|[1-9][0-9]{1,3}(\\.[0-9]+)?|[1-5][0-9]{4}(\\.[0-9]+)?|6[0-3][0-9]{3}(\\.[0-9]+)?|64000)M$"
              x-ui-placeholder: "e.g., '800M' or '1.5G'"
              x-ui-error-message: "Value doesn't match pattern, it should be number ranging from 1G to 64G or 1M to 64000M"
            cpu_limit:
              type: string
              title: CPU Limit
              description: Maximum CPU resource utilization.
              x-ui-compare: 
                field: spec.size.reader.cpu
                comparator: '>='
                x-ui-error-message: 'CPU limit cannot be less than CPU'
              pattern: "^([1-9]|[12][0-9]|3[0-2])$|^(3[0-1][0-9]{3}|[1-2][0-9]{4}|[1-9][0-9]{0,3}|32000)m$"
              x-ui-placeholder: "e.g., '500m' or '1'"
              x-ui-error-message: "Value doesn't match pattern, it should be number ranging from 1 to 32 or 1m to 32000m"
            memory_limit:
              type: string
              title: Memory Limit
              description: Maximum memory resource utilization.
              x-ui-compare: 
                field: spec.size.reader.memory
                comparator: '>='
                x-ui-error-message: 'Memory limit cannot be less than memory'
              pattern: "^(0\\.[1-9]|[1-9](\\.[0-9]+)?|[1-5][0-9](\\.[0-9]+)?|6[0-4])G$|^([1-9](\\.[0-9]+)?|[1-9][0-9]{1,3}(\\.[0-9]+)?|[1-5][0-9]{4}(\\.[0-9]+)?|6[0-3][0-9]{3}(\\.[0-9]+)?|64000)M$"
              x-ui-placeholder: "e.g., '800M' or '1.5G'"
              x-ui-error-message: "Value doesn't match pattern, it should be number ranging from 1G to 64G or 1M to 64000M"
            volume:
              type: string
              title: Volume
              pattern: "^[0-9]+(\\.[0-9]+)?[G]i?$"
              x-ui-placeholder: "e.g., '10Gi' or '50Gi'"
              x-ui-error-message: "Volume must be specified in the correct format with integer only (e.g., '10Gi' or '50Gi')."
              x-ui-visible-if: 
                field: spec.persistence_enabled
                values: [ true ]
          required:
            - instance_count
            - cpu
            - memory
        writer:
          type: object
          title: Writer
          description: Settings for writer instances.
          properties:
            cpu:
              type: string
              title: CPU
              description: Number of CPU cores required.
              x-ui-compare: 
                field: spec.size.writer.cpu_limit
                comparator: '<='
                x-ui-error-message: 'CPU cannot be more than CPU limit'
              pattern: "^([1-9]|[12][0-9]|3[0-2])$|^(3[0-1][0-9]{3}|[1-2][0-9]{4}|[1-9][0-9]{0,3}|32000)m$"
              x-ui-placeholder: "e.g., '500m' or '1'"
              x-ui-error-message: "Value doesn't match pattern, it should be number ranging from 1 to 32 or 1m to 32000m"
            memory:
              type: string
              title: Memory
              description: Amount of memory required.
              x-ui-compare: 
                field: spec.size.writer.memory_limit
                comparator: '<='
                x-ui-error-message: 'Memory cannot be more than memory limit'
              pattern: "^(0\\.[1-9]|[1-9](\\.[0-9]+)?|[1-5][0-9](\\.[0-9]+)?|6[0-4])G$|^([1-9](\\.[0-9]+)?|[1-9][0-9]{1,3}(\\.[0-9]+)?|[1-5][0-9]{4}(\\.[0-9]+)?|6[0-3][0-9]{3}(\\.[0-9]+)?|64000)M$"
              x-ui-placeholder: "e.g., '800M' or '1.5G'"
              x-ui-error-message: "Value doesn't match pattern, it should be number ranging from 1G to 64G or 1M to 64000M"
            cpu_limit:
              type: string
              title: CPU Limit
              description: Maximum CPU resource utilization.
              x-ui-compare: 
                field: spec.size.writer.cpu
                comparator: '>='
                x-ui-error-message: 'CPU limit cannot be less than CPU'
              pattern: "^([1-9]|[12][0-9]|3[0-2])$|^(3[0-1][0-9]{3}|[1-2][0-9]{4}|[1-9][0-9]{0,3}|32000)m$"
              x-ui-placeholder: "e.g., '500m' or '1'"
              x-ui-error-message: "Value doesn't match pattern, it should be number ranging from 1 to 32 or 1m to 32000m"
            memory_limit:
              type: string
              title: Memory Limit
              description: Maximum memory resource utilization.
              x-ui-compare: 
                field: spec.size.writer.memory
                comparator: '>='
                x-ui-error-message: 'Memory limit cannot be less than memory'
              pattern: "^(0\\.[1-9]|[1-9](\\.[0-9]+)?|[1-5][0-9](\\.[0-9]+)?|6[0-4])G$|^([1-9](\\.[0-9]+)?|[1-9][0-9]{1,3}(\\.[0-9]+)?|[1-5][0-9]{4}(\\.[0-9]+)?|6[0-3][0-9]{3}(\\.[0-9]+)?|64000)M$"
              x-ui-placeholder: "e.g., '800M' or '1.5G'"
              x-ui-error-message: "Value doesn't match pattern, it should be number ranging from 1G to 64G or 1M to 64000M"
            volume:
              type: string
              title: Volume
              pattern: "^[0-9]+(\\.[0-9]+)?[G]i?$"
              x-ui-placeholder: "e.g., '10Gi' or '50Gi'"
              x-ui-error-message: "Volume must be specified in the correct format with integer only (e.g., '10Gi' or '50Gi')."
              x-ui-visible-if: 
                field: spec.persistence_enabled
                values: [ true ]
          required:
            - cpu
            - memory
  required:
    - size
    - authenticated
    - persistence_enabled
    - redis_version
  x-ui-order:
    - authenticated
    - persistence_enabled
    - redis_version
    - size
sample:
  flavor: k8s
  metadata: {}
  $schema: "https://facets-cloud.github.io/facets-schemas/schemas/redis/redis.schema.json"
  kind: redis
  disabled: true
  version: "0.1"
  spec:
    authenticated: true
    redis_version: "7.4.1"
    persistence_enabled: false
    size:
      writer:
        cpu: "1"
        memory: "1G"
        cpu_limit: "1"
        memory_limit: "1G"
        volume: "50Gi"
      reader:
        cpu: "1"
        memory: "1G"
        cpu_limit: "1"
        memory_limit: "1G"
        volume: "1Gi"
        instance_count: 0
