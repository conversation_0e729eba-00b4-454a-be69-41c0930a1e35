intent: alb
flavor: default
version: '0.1'
description: Adds alb - default flavor
clouds:
- aws
spec:
  title: AWS ALB Ingress Controller
  type: object
  properties:
    private:
      type: boolean
      title: Private
      description: Set load balancer as private
    domains:
      title: Domains
      description: Map of domain key to rules
      type: object
      x-ui-overrides-only: false
      patternProperties:
        keyPattern: "^[a-zA-Z0-9_.-]*$"
        title: Domain Object
        description: Name of the domain object
        properties:
          domain:
            type: string
            title: Domain
            description: Host name of the domain
            pattern: '^(?=.{1,253}$)(?!-)[A-Za-z0-9-]{1,63}(?<!-)(\.[A-Za-z0-9-]{1,63})*\.[A-Za-z]{2,6}$'
            x-ui-placeholder: "Domain to map ingress. Eg: example.com, sub.example.com, my-domain.co.uk"
            x-ui-error-message: "Value doesn't match the format. Eg: example.com, my-domain.co.uk"
          alias:
            type: string
            title: Alias
            description: Alias for the domain
          certificate_reference:
            type: string
            title: Certificate reference
            Description: Certificate reference name. It's the ACM ARN certificate reference.
            pattern: '^arn:aws:acm:\w+(?:-\w+)+:\d{12}:certificate\/[A-Za-z0-9]+(?:-[A-Za-z0-9]+)+$'
            x-ui-placeholder: "IAM policy example. Eg: arn:aws:acm:us-west-2:123456789012:certificate/abcd1234-5678-90ab-cdef-EXAMPLE11111"
            x-ui-error-message: "Value doesn't match pattern, accepted value pattern Eg: arn:aws:acm:us-west-2:123456789012:certificate/abcd1234-5678-90ab-cdef-EXAMPLE11111"
        required: [ "domain", "alias", "certificate_reference" ]
    rules:
      title: Ingress Rules
      description: Objects of all ingress rules
      type: object
      patternProperties:
        keyPattern: "^[a-zA-Z0-9_.-]*$"
        title: Ingress Object
        description: Name of the ingress object
        properties:
          service_name:
            type: string
            title: Service Name
            description: Kubernetes service name
            x-ui-api-source:
              endpoint: "/cc-ui/v1/dropdown/stack/{{stackName}}/resources-info"
              method: GET
              params:
                includeContent: false
              labelKey: resourceName
              valueKey: resourceName
              valueTemplate: "${service.{{value}}.out.attributes.service_name}"
              filterConditions:
                - field: resourceType
                  value: service
            x-ui-typeable: true
          path:
            type: string
            title: Path
            description: Path of the application
            pattern: '^(/[^/]+)*(/)?$'
            x-ui-placeholder: "Enter path to which your application will be accessible"
            x-ui-error-message: "Value doesn't match pattern, eg: / or /<path> etc"
            default: '/'
          port:
            type: integer
            title: Port
            description: Service port number
            x-ui-api-source:
              endpoint: "/cc-ui/v1/dropdown/stack/{{stackName}}/service/{{serviceName}}/overview"
              dynamicProperties:
                serviceName:
                  key: service_name
                  lookup: regex
                  x-ui-lookup-regex: '\${[^.]+\.([^.]+).*'
              method: GET
              labelKey: port
              valueKey: port
              path: ports
            x-ui-disable-tooltip: "No Ports Found or Service not selected"
            x-ui-typeable: true
          domain_prefix:
            type: string
            title: Domain Prefix
            description: Subdomain prefix
            pattern: '^(?!-)[a-z0-9-]{1,18}(?<!-)$'
            x-ui-placeholder: "Enter the subdomain you want to expose your application"
            x-ui-error-message: "Value doesn't match pattern, max characters of 20, only hyphen special characters is accepted with numbers and alphabets and should not start or end with hyphen"
          port_name:
            type: string
            title: Port name
            description: Service port name
## TODO: once support is avaialable from FE
#            x-ui-api-source:
#              endpoint: "/cc-ui/v1/dropdown/stack/{{stackName}}/resources-info"
#              method: GET
#              params:
#                includeContent: false
#              labelKey: resourceName
#              valueKey: resourceName
#              valueTemplate: "${service.{{value}}.out.attributes.{{value}}.port_name}"
#              filterConditions:
#                - field: resourceType
#                  value: service
#            x-ui-typeable: true
          priority:
            type: string
            title: Priority
            description: Priority number for the rule
            minimum: 1
            maximum: 1000
            x-ui-placeholder: "Enter Priority number for your application"
            x-ui-error-message: "Value doesn't match pattern, number should be ranging from 1-1000"
        required: ["service_name", "path", "port", "priority", "port_name", "domain_prefix"]
    force_ssl_redirection:
      type: boolean
      title: Force SSL Redirection
      description: Force HTTP to HTTPS redirection
  required: ["private", "rules", "force_ssl_redirection"]
  x-ui-order: ["private", "force_ssl_redirection", "domains", "rules"]
sample:
  $schema: https://facets-cloud.github.io/facets-schemas/schemas/alb/alb.schema.json
  kind: alb
  flavor: default
  metadata: {}
  disabled: true
  version: '0.1'
  spec:
    domains:
    force_ssl_redirection: true
    private: false
    rules:
      rule1:
        domain_prefix: rule1
        path: /test
        port: '8080'
        port_name: http-svc
        priority: 1
        service_name: cart-service
