intent: mysql_web
flavor: k8s
version: "0.1"
clouds:
  - aws
  - kubernetes
  - azure
  - gcp
description: Provides Gui For Mysql Users to login
inputs:
  default:
    type: "@output/mysql"
    adds_capability: true
metadata:
  title: Metadata of Mysql Web UI
  type: object
  properties:
    namespace:
      type: string
      title: Namespace 
      description: Namespace in which UI should be deployed
      default: default
spec:
  title: Mysql Web Ui
  type: object
  properties:
    mysql_web_version:
      type: string
      title: Ui version
      description: Version of Mysql Ui 
      x-ui-placeholder: "Ex. 5.2.1-debian-12-r35"
    size:
      type: object
      title: Size
      description: Datastore Sizing
      properties:
        cpu:
          type: string
          title: CPU
          description: Number of CPU cores
          minLength: 1
          pattern: "^([1-9]|[12][0-9]|3[0-2])$|^(3[0-1][0-9]{3}|[1-2][0-9]{4}|[1-9][0-9]{0,3}|32000)m$"
          x-ui-placeholder: "e.g., '500m' or '1'"
          x-ui-error-message: "Value doesn't match pattern, it should be number ranging from 1 to 32 or 1m to 32000m"
        memory:
          type: string
          title: Memory
          description: Amount of memory
          minLength: 1
          pattern: "^(0\\.[1-9]|[1-9](\\.[0-9]+)?|[1-5][0-9](\\.[0-9]+)?|6[0-4])Gi$|^([1-9](\\.[0-9]+)?|[1-9][0-9]{1,3}(\\.[0-9]+)?|[1-5][0-9]{4}(\\.[0-9]+)?|6[0-3][0-9]{3}(\\.[0-9]+)?|64000)Mi$"            
          x-ui-placeholder: "e.g., '800Mi' or '1.5Gi'"
          x-ui-error-message: "Value doesn't match pattern, it should be number ranging from 1Gi to 64Gi or 1Mi to 64000Mi"
        cpu_limit:
          type: string
          title: CPU Limit
          description: CPU limit
          minLength: 1
          pattern: "^([1-9]|[12][0-9]|3[0-2])$|^(3[0-1][0-9]{3}|[1-2][0-9]{4}|[1-9][0-9]{0,3}|32000)m$"
          x-ui-placeholder: "e.g., '500m' or '1'"
          x-ui-error-message: "Value doesn't match pattern, it should be number ranging from 1 to 32 or 1m to 32000m"
        memory_limit:
          type: string
          title: Memory Limit
          description: Memory limit
          minLength: 1
          pattern: "^(0\\.[1-9]|[1-9](\\.[0-9]+)?|[1-5][0-9](\\.[0-9]+)?|6[0-4])Gi$|^([1-9](\\.[0-9]+)?|[1-9][0-9]{1,3}(\\.[0-9]+)?|[1-5][0-9]{4}(\\.[0-9]+)?|6[0-3][0-9]{3}(\\.[0-9]+)?|64000)Mi$"
          x-ui-placeholder: "e.g., '800Mi' or '1.5Gi'"
          x-ui-error-message: "Value doesn't match pattern, it should be number ranging from 1Gi to 64Gi or 1Mi to 64000Mi"
sample:
  kind: mysql_web
  flavor: k8s
  version: "0.1"
  metadata: {}
  disabled: true
  spec:
    mysql_version: "5.2.1-debian-12-r35"
    size:
      cpu: "500m"
      memory: "1Gi"
