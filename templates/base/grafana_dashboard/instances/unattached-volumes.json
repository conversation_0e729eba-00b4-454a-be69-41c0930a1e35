{"disabled": false, "kind": "grafana_dashboard", "flavor": "default", "version": "latest", "lifecycle": "ENVIRONMENT", "metadata": {"name": "unattached-volumes"}, "ui": {"base_resource": true}, "spec": {"dashboard": {"annotations": {"list": [{"builtIn": 1, "datasource": {"type": "grafana", "uid": "-- <PERSON><PERSON> --"}, "enable": true, "hide": true, "iconColor": "rgba(0, 211, 255, 1)", "name": "Annotations & Alerts", "target": {"limit": 100, "matchAny": false, "tags": [], "type": "dashboard"}, "type": "dashboard"}]}, "editable": true, "fiscalYearStartMonth": 0, "graphTooltip": 0, "id": 27, "links": [], "liveNow": false, "panels": [{"datasource": {"type": "prometheus", "uid": "prometheus"}, "description": "", "fieldConfig": {"defaults": {"color": {"mode": "thresholds"}, "custom": {"align": "auto", "displayMode": "auto", "filterable": false, "inspect": false, "width": 20}, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": null}, {"color": "red", "value": 80}]}}, "overrides": [{"matcher": {"id": "by<PERSON><PERSON>", "options": "__name__"}, "properties": [{"id": "custom.hidden", "value": true}]}, {"matcher": {"id": "by<PERSON><PERSON>", "options": "Time"}, "properties": [{"id": "custom.width", "value": 178}, {"id": "custom.hidden", "value": true}]}, {"matcher": {"id": "by<PERSON><PERSON>", "options": "availability_zone"}, "properties": [{"id": "custom.width", "value": 224}]}, {"matcher": {"id": "by<PERSON><PERSON>", "options": "cluster_name"}, "properties": [{"id": "custom.hidden", "value": false}, {"id": "custom.width", "value": 330}]}, {"matcher": {"id": "by<PERSON><PERSON>", "options": "container"}, "properties": [{"id": "custom.hidden", "value": true}]}, {"matcher": {"id": "by<PERSON><PERSON>", "options": "facets_tags"}, "properties": [{"id": "custom.width", "value": 371}]}, {"matcher": {"id": "by<PERSON><PERSON>", "options": "endpoint"}, "properties": [{"id": "custom.hidden", "value": true}]}, {"matcher": {"id": "by<PERSON><PERSON>", "options": "instance"}, "properties": [{"id": "custom.hidden", "value": true}]}, {"matcher": {"id": "by<PERSON><PERSON>", "options": "Value"}, "properties": [{"id": "custom.hidden", "value": true}]}, {"matcher": {"id": "by<PERSON><PERSON>", "options": "pod"}, "properties": [{"id": "custom.hidden", "value": true}]}, {"matcher": {"id": "by<PERSON><PERSON>", "options": "job"}, "properties": [{"id": "custom.hidden", "value": true}]}, {"matcher": {"id": "by<PERSON><PERSON>", "options": "service"}, "properties": [{"id": "custom.hidden", "value": true}]}, {"matcher": {"id": "by<PERSON><PERSON>", "options": "namespace"}, "properties": [{"id": "custom.hidden", "value": true}]}, {"matcher": {"id": "by<PERSON><PERSON>", "options": "volume_id"}, "properties": [{"id": "custom.width", "value": 424}]}]}, "gridPos": {"h": 20, "w": 24, "x": 0, "y": 0}, "id": 2, "interval": "2m", "options": {"footer": {"fields": "", "reducer": ["sum"], "show": false}, "showHeader": true, "sortBy": []}, "pluginVersion": "9.2.15", "targets": [{"datasource": {"type": "prometheus", "uid": "prometheus"}, "editorMode": "code", "exemplar": false, "expr": "count(leaked_volume) by (volume_id,facets_tags,availability_zone,cluster_name)", "format": "table", "instant": false, "interval": "", "legendFormat": "__auto", "range": true, "refId": "A"}], "title": "Unattached Volumes", "transformations": [{"id": "organize", "options": {"excludeByName": {}, "indexByName": {"Time": 1, "Value": 13, "__name__": 2, "availability_zone": 3, "cluster_name": 4, "container": 5, "endpoint": 6, "facets_tags": 7, "instance": 8, "job": 9, "namespace": 10, "pod": 11, "service": 12, "volume_id": 0}, "renameByName": {}}}], "type": "table"}], "schemaVersion": 37, "style": "dark", "tags": [], "templating": {"list": []}, "time": {"from": "now-20s", "to": "now"}, "timepicker": {}, "timezone": "", "title": "Unattached Volumes", "uid": "3yhuLZQ4k", "version": 3, "weekStart": ""}}, "advanced": {"inherit_from_base": true, "default": {"preserve_uid": true}}}