intent: elasticsearch
flavor: opensearch
version: '0.1'
description: Adds Opensearch module of intent Elasticsearch
clouds:
  - aws
spec:
  title: OpenSearch
  type: object
  properties:
    elasticsearch_version:
      type: string
      title: Elasticsearch Version
      description: Version of Elasticsearch. Refer to the url for more information https://docs.aws.amazon.com/opensearch-service/latest/developerguide/what-is.html
      minLength: 1
      x-ui-placeholder: "Ex. 7.10"
      enum:
        - "7.10"
        - "6.8"
        - "5.6"
        - "2.3"
        - "1.5"
    size:
      type: object
      title: Size
      description: Datastore Sizing
      properties:
        instance:
          type: string
          title: Instance Type
          description: Instance type of the node
          enum:
            - or1.medium.elasticsearch
            - or1.large.elasticsearch
            - or1.xlarge.elasticsearch
            - or1.2xlarge.elasticsearch
            - or1.4xlarge.elasticsearch
            - or1.8xlarge.elasticsearch
            - or1.12xlarge.elasticsearch
            - or1.16xlarge.elasticsearch
            - im4gn.large.elasticsearch
            - im4gn.xlarge.elasticsearch
            - im4gn.2xlarge.elasticsearch
            - im4gn.4xlarge.elasticsearch
            - im4gn.8xlarge.elasticsearch
            - im4gn.16xlarge.elasticsearch
            - c5.large.elasticsearch
            - c5.xlarge.elasticsearch
            - c5.2xlarge.elasticsearch
            - c5.4xlarge.elasticsearch
            - c5.9xlarge.elasticsearch
            - c5.18xlarge.elasticsearch
            - c6g.large.elasticsearch
            - c6g.xlarge.elasticsearch
            - c6g.2xlarge.elasticsearch
            - c6g.4xlarge.elasticsearch
            - c6g.8xlarge.elasticsearch
            - c6g.12xlarge.elasticsearch
            - i3.large.elasticsearch
            - i3.xlarge.elasticsearch
            - i3.2xlarge.elasticsearch
            - i3.4xlarge.elasticsearch
            - i3.8xlarge.elasticsearch
            - i3.16xlarge.elasticsearch
            - m5.large.elasticsearch
            - m5.xlarge.elasticsearch
            - m5.2xlarge.elasticsearch
            - m5.4xlarge.elasticsearch
            - m5.12xlarge.elasticsearch
            - m6g.large.elasticsearch
            - m6g.xlarge.elasticsearch
            - m6g.2xlarge.elasticsearch
            - m6g.4xlarge.elasticsearch
            - m6g.8xlarge.elasticsearch
            - m6g.12xlarge.elasticsearch
            - r5.large.elasticsearch
            - r5.xlarge.elasticsearch
            - r5.2xlarge.elasticsearch
            - r5.4xlarge.elasticsearch
            - r5.12xlarge.elasticsearch
            - r6g.large.elasticsearch
            - r6g.xlarge.elasticsearch
            - r6g.2xlarge.elasticsearch
            - r6g.4xlarge.elasticsearch
            - r6g.8xlarge.elasticsearch
            - r6g.12xlarge.elasticsearch
            - r6gd.large.elasticsearch
            - r6gd.xlarge.elasticsearch
            - r6gd.2xlarge.elasticsearch
            - r6gd.4xlarge.elasticsearch
            - r6gd.8xlarge.elasticsearch
            - r6gd.12xlarge.elasticsearch
            - r6gd.16xlarge.elasticsearch
            - t3.small.elasticsearch
            - t3.medium.elasticsearch
            - c4.large.elasticsearch
            - c4.xlarge.elasticsearch
            - c4.2xlarge.elasticsearch
            - c4.4xlarge.elasticsearch
            - c4.8xlarge.elasticsearch
            - i2.xlarge.elasticsearch
            - i2.2xlarge.elasticsearch
            - m3.medium.elasticsearch
            - m3.large.elasticsearch
            - m3.xlarge.elasticsearch
            - m3.2xlarge.elasticsearch
            - m4.large.elasticsearch
            - m4.xlarge.elasticsearch
            - m4.2xlarge.elasticsearch
            - m4.4xlarge.elasticsearch
            - m4.10xlarge.elasticsearch
            - r3.large.elasticsearch
            - r3.xlarge.elasticsearch
            - r3.2xlarge.elasticsearch
            - r3.4xlarge.elasticsearch
            - r3.8xlarge.elasticsearch
            - r4.large.elasticsearch
            - r4.xlarge.elasticsearch
            - r4.2xlarge.elasticsearch
            - r4.4xlarge.elasticsearch
            - r4.8xlarge.elasticsearch
            - r4.16xlarge.elasticsearch
            - t2.micro.elasticsearch
            - t2.small.elasticsearch
            - t2.medium.elasticsearch
          x-ui-placeholder: "Select instance type"
        instance_count:
          type: integer
          title: Instance Count
          description: The number of instances to create
          minimum: 1
          maximum: 20
          x-ui-placeholder: "Minimum value 1"
        volume:
          type: string
          title: Volume
          description: The size of the volume
          pattern: "^[0-9]+[G]i?$"
          x-ui-placeholder: "Enter volume size"
          x-ui-error-message: "Value doesn't match '^[0-9]+[G]i?$' pattern"
      required:
        - instance
        - instance_count
        - volume
  required:
    - size
    - elasticsearch_version
  x-ui-order:
    - elasticsearch_version
    - size
sample:
  $schema: "https://facets-cloud.github.io/facets-schemas/schemas/elasticsearch/elasticsearch.schema.json"
  flavor: opensearch
  kind: elasticsearch
  metadata: { }
  spec:
    elasticsearch_version: '7.10'
    size:
      instance: r4.large.elasticsearch
      instance_count: 2
      volume: 100
  version: '0.1'
  disabled: true
