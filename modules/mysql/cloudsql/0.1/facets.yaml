intent: mysql
flavor: cloudsql
version: "0.1"
description: Adds MYSQL module of flavor cloudsql
clouds:
  - gcp
spec:
  title: CloudSQL
  type: object
  properties:
    mysql_version:
      type: string
      title: MySQL Version
      description: Version of MySQL
      minLength: 1
      x-ui-placeholder: "Ex. 8.0"
      enum:
        - "8.0"
        - "5.7"
        - "5.6"
    size:
      type: object
      title: Size
      description: Writer and Reader Datastore Sizing
      properties:
        reader:
          type: object
          title: Reader
          description: Reader Node Configuration
          properties:
            instance:
              type: string
              title: Instance Type
              description: Type of Instance for Reader
              enum:
                - db-f1-micro
                - db-g1-small
                - db-n1-standard-1
                - db-n1-standard-2
                - db-n1-standard-4
                - db-n1-standard-8
                - db-n1-standard-16
                - db-n1-standard-32
                - db-n1-standard-64
                - db-n1-highmem-2
                - db-n1-highmem-4
                - db-n1-highmem-8
                - db-n1-highmem-16
                - db-n1-highmem-32
                - db-n1-highmem-64
                - db-n1-highcpu-2
                - db-n1-highcpu-4
                - db-n1-highcpu-8
                - db-n1-highcpu-16
                - db-n1-highcpu-32
                - db-n1-highcpu-64
                - db-custom-1-3840
                - db-custom-2-7680
                - db-custom-4-15360
                - db-custom-8-30720
                - db-custom-16-61440
                - db-custom-32-122880
              x-ui-placeholder: "Select reader instance type"
            volume:
              type: string
              title: Volume
              description: Volume Size for Reader
              pattern: "^[0-9]+[G]i?$"
              x-ui-placeholder: "Specify reader volume size"
              x-ui-error-message: "Value doesn't match '^[0-9]+[G]i?$' pattern"
            instance_count:
              type: integer
              title: Instance Count
              description: Number of Reader Instances
              minimum: 0
              maximum: 20
              x-ui-placeholder: "Enter reader instance count"
              x-ui-error-message: "Instance count must be a non-negative integer"
          required:
            - instance
            - volume
            - instance_count
        writer:
          type: object
          title: Writer
          description: Writer Node Configuration
          properties:
            instance:
              type: string
              title: Instance Type
              description: Type of Instance for Writer
              enum:
                - db-f1-micro
                - db-g1-small
                - db-n1-standard-1
                - db-n1-standard-2
                - db-n1-standard-4
                - db-n1-standard-8
                - db-n1-standard-16
                - db-n1-standard-32
                - db-n1-standard-64
                - db-n1-highmem-2
                - db-n1-highmem-4
                - db-n1-highmem-8
                - db-n1-highmem-16
                - db-n1-highmem-32
                - db-n1-highmem-64
                - db-n1-highcpu-2
                - db-n1-highcpu-4
                - db-n1-highcpu-8
                - db-n1-highcpu-16
                - db-n1-highcpu-32
                - db-n1-highcpu-64
                - db-custom-1-3840
                - db-custom-2-7680
                - db-custom-4-15360
                - db-custom-8-30720
                - db-custom-16-61440
                - db-custom-32-122880
              x-ui-placeholder: "Select writer instance type"
            volume:
              type: string
              title: Volume
              description: Volume Size for Writer
              pattern: "^[0-9]+[G]i?$"
              x-ui-placeholder: "Specify writer volume size"
              x-ui-error-message: "Value doesn't match '^[0-9]+[G]i?$' pattern"
          required:
            - instance
            - volume
  required:
    - size
    - mysql_version
  x-ui-order:
    - mysql_version
    - size
sample:
  $schema: "https://facets-cloud.github.io/facets-schemas/schemas/mysql/mysql.schema.json"
  kind: mysql
  flavor: cloudsql
  version: "0.1"
  disabled: true
  metadata:
    tags:
      managed-by: facets
  spec:
    mysql_version: "8.0"
    size:
      writer:
        instance: db-f1-micro
        volume: 10G
      reader:
        instance: db-f1-micro
        volume: 10G
        instance_count: 0
