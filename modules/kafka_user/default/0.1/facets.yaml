intent: kafka_user
flavor: default
version: '0.1'
description: Adds kafka_user - default flavor
clouds:
- aws
- gcp
- azure
- kubernetes
spec:
  title: Kafka User
  type: object
  description: Specifications of user for Kafka
sample:
  $schema: https://facets-cloud.github.io/facets-schemas/schemas/kafka_user/kafka_user.schema.json
  kind: kafka_user
  flavor: default
  version: '0.1'
  disabled: false
  metadata: {}
  out: {}
  spec: { }
