intent: vault
flavor: k8s
version: "0.1"
description: Adds vault module of kubernetes flavor
clouds:
  - aws
  - azure
  - gcp
  - kubernetes
inputs:
  kubernetes_details:
    optional: false
    type: "@output/kubernetes"
    default:
      resource_type: kubernetes_cluster
      resource_name: default
spec:
  title: K8s
  type: object
  properties:
    vault_version:
      type: string
      title: Vault Version
      description: Specifies the Vault version to use.
      enum:
        - "1.17.2"
      x-ui-placeholder: "e.g., '1.17.2'"
      x-ui-error-message: "Please select a valid Vault version."
    size:
      type: object
      title: Size
      description: Size of the Vault instance.
      properties:
        instance_count:
          type: integer
          title: Instance Count
          description: Number of instances to create.
          minimum: 1
          maximum: 10
          x-ui-placeholder: "e.g., 1 or 3"
          x-ui-error-message: "Instance count must be between 1 and 10."
        cpu:
          type: string
          title: CPU
          description: Number of CPU cores required.
          minLength: 1
          pattern: "^([1-9]|[12][0-9]|3[0-2])$|^(3[0-1][0-9]{3}|[1-2][0-9]{4}|[1-9][0-9]{0,3}|32000)m$"
          x-ui-compare:
            field: spec.size.cpu_limit
            comparator: "<="
            x-ui-error-message: "CPU cannot be more than CPU limit"
          x-ui-placeholder: "e.g., '256m' or '1'"
          x-ui-error-message: "Value doesn't match pattern, it should be number ranging from 1 to 32 or 1m to 32000m"
        memory:
          type: string
          title: Memory
          description: Amount of memory required.
          minLength: 1
          pattern: "^([1-9]|[1-5][0-9]|6[0-4])Gi$|^([1-9]|[1-9][0-9]{1,3}|[1-5][0-9]{4}|6[0-3][0-9]{3}|64000)Mi$"
          x-ui-compare:
            field: spec.size.memory_limit
            comparator: "<="
            x-ui-error-message: "Memory cannot be more than memory limit"
          x-ui-placeholder: "e.g., '256Mi' or '1.5Gi'"
          x-ui-error-message: "Value doesn't match pattern, it should be number ranging from 1Gi to 64Gi or 1Mi to 64000Mi"
        cpu_limit:
          type: string
          title: CPU Limit
          description: Set a maximum limit on CPU utilization.
          minLength: 1
          pattern: "^([1-9]|[12][0-9]|3[0-2])$|^(3[0-1][0-9]{3}|[1-2][0-9]{4}|[1-9][0-9]{0,3}|32000)m$"
          x-ui-compare:
            field: spec.size.cpu
            comparator: ">="
            x-ui-error-message: "CPU limit cannot be less than CPU"
          x-ui-placeholder: "e.g., '256m' or '1'"
          x-ui-error-message: "Value doesn't match pattern, it should be number ranging from 1 to 32 or 1m to 32000m"
        memory_limit:
          type: string
          title: Memory Limit
          description: Set a maximum limit on memory utilization.
          minLength: 1
          pattern: "^([1-9]|[1-5][0-9]|6[0-4])Gi$|^([1-9]|[1-9][0-9]{1,3}|[1-5][0-9]{4}|6[0-3][0-9]{3}|64000)Mi$"
          x-ui-compare:
            field: spec.size.memory
            comparator: ">="
            x-ui-error-message: "Memory limit cannot be less than memory"
          x-ui-placeholder: "e.g., '256Mi' or '1.5Gi'"
          x-ui-error-message: "Value doesn't match pattern, it should be number ranging from 1Gi to 64Gi or 1Mi to 64000Mi"
        volume:
          type: string
          title: Volume
          description: The size of the volume.
          minLength: 1
          pattern: "^[0-9]+(\\.[0-9]+)?[G]i?$"
          x-ui-placeholder: "e.g., '10Gi' or '50Gi'"
          x-ui-error-message: "Volume must be specified in the correct format (e.g., '10Gi' or '50Gi')."
      required:
        - instance_count
        - cpu
        - memory
        - volume
  required:
    - size
    - vault_version
  x-ui-order:
    - vault_version
    - size
sample:
  $schema: >-
    https://facets-cloud.github.io/facets-schemas/schemas/vault/vault.schema.json
  flavor: k8s
  metadata: {}
  kind: vault
  disabled: true
  version: "0.1"
  spec:
    authenticated: true
    vault_version: "1.17.2"
    size:
      instance_count: 1
      cpu: "250m"
      memory: "256Mi"
      cpu_limit: "250m"
      memory_limit: "256Mi"
      volume: "10Gi"
