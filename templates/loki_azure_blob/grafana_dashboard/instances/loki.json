{"kind": "grafana_dashboard", "flavor": "default", "version": "latest", "lifecycle": "ENVIRONMENT", "disabled": false, "conditional_on_intent": "log_collector", "provided": false, "depends_on": [], "metadata": {"name": "loki"}, "spec": {"dashboard": {"annotations": {"list": [{"builtIn": 1, "datasource": {"type": "grafana", "uid": "-- <PERSON><PERSON> --"}, "enable": true, "hide": true, "iconColor": "rgba(0, 211, 255, 1)", "name": "Annotations & Alerts", "target": {"limit": 100, "matchAny": false, "tags": [], "type": "dashboard"}, "type": "dashboard"}]}, "description": "", "editable": true, "fiscalYearStartMonth": 0, "graphTooltip": 0, "id": 34, "links": [{"asDropdown": true, "icon": "external link", "includeVars": true, "keepTime": true, "tags": ["Facets", "loki"], "targetBlank": false, "title": "Loki Dashboards", "type": "dashboards"}], "liveNow": false, "panels": [{"datasource": {"type": "prometheus", "uid": "$datasource"}, "fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}, "custom": {"axisCenteredZero": false, "axisColorMode": "text", "axisLabel": "", "axisPlacement": "auto", "barAlignment": 0, "drawStyle": "line", "fillOpacity": 10, "gradientMode": "none", "hideFrom": {"legend": false, "tooltip": false, "viz": false}, "lineInterpolation": "linear", "lineStyle": {"fill": "solid"}, "lineWidth": 2, "pointSize": 5, "scaleDistribution": {"log": 2, "type": "log"}, "showPoints": "never", "spanNulls": false, "stacking": {"group": "A", "mode": "none"}, "thresholdsStyle": {"mode": "off"}}, "links": [], "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": null}]}, "unit": "short"}, "overrides": []}, "gridPos": {"h": 7, "w": 8, "x": 0, "y": 0}, "id": 6, "options": {"legend": {"calcs": [], "displayMode": "list", "placement": "bottom", "showLegend": true}, "tooltip": {"mode": "single", "sort": "none"}}, "panels": [], "pluginVersion": "9.2.6", "targets": [{"datasource": {"uid": "$datasource"}, "editorMode": "code", "expr": "kube_daemonset_status_desired_number_scheduled{daemonset=~\".*promtail\"} - kube_daemonset_status_number_ready{daemonset=~\".*promtail\"}", "legendFormat": "{{daemonset}}", "range": true, "refId": "A"}, {"datasource": {"type": "prometheus", "uid": "$datasource"}, "editorMode": "code", "expr": "kube_deployment_status_replicas{deployment=~\".*distributor\"} - kube_deployment_status_replicas_available{deployment=~\".*distributor\"}", "hide": false, "legendFormat": "{{deployment}}", "range": true, "refId": "B"}, {"datasource": {"type": "prometheus", "uid": "$datasource"}, "editorMode": "code", "expr": "kube_statefulset_status_replicas{statefulset=~\".*ingester\"} - kube_statefulset_status_replicas_ready{statefulset=~\".*ingester\"}", "hide": false, "legendFormat": "{{statefulset}}", "range": true, "refId": "C"}, {"datasource": {"type": "prometheus", "uid": "$datasource"}, "editorMode": "code", "expr": "kube_deployment_status_replicas{deployment=~\".*query-frontend\"} - kube_deployment_status_replicas_available{deployment=~\".*query-frontend\"}", "hide": false, "legendFormat": "{{deployment}}", "range": true, "refId": "D"}, {"datasource": {"type": "prometheus", "uid": "$datasource"}, "editorMode": "code", "expr": "kube_statefulset_status_replicas{statefulset=~\".*querier\"} - kube_statefulset_status_replicas_ready{statefulset=~\".*querier\"}", "hide": false, "legendFormat": "{{statefulset}}", "range": true, "refId": "E"}], "title": "Non Ready Pods", "type": "timeseries"}, {"datasource": {"type": "prometheus", "uid": "$datasource"}, "fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}, "custom": {"axisCenteredZero": false, "axisColorMode": "text", "axisLabel": "", "axisPlacement": "auto", "barAlignment": 0, "drawStyle": "line", "fillOpacity": 10, "gradientMode": "none", "hideFrom": {"legend": false, "tooltip": false, "viz": false}, "lineInterpolation": "linear", "lineStyle": {"fill": "solid"}, "lineWidth": 2, "pointSize": 5, "scaleDistribution": {"log": 2, "type": "log"}, "showPoints": "never", "spanNulls": false, "stacking": {"group": "A", "mode": "none"}, "thresholdsStyle": {"mode": "off"}}, "links": [], "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": null}]}, "unit": "short"}, "overrides": []}, "gridPos": {"h": 7, "w": 8, "x": 8, "y": 0}, "id": 123, "options": {"legend": {"calcs": [], "displayMode": "list", "placement": "bottom", "showLegend": true}, "tooltip": {"mode": "single", "sort": "none"}}, "panels": [], "pluginVersion": "9.2.6", "targets": [{"datasource": {"type": "prometheus", "uid": "$datasource"}, "editorMode": "code", "expr": "increase(kube_pod_container_status_restarts_total{container=~\"compactor|query-frontend|querier|ingester|distributor|promtail\"}[5m]) > 0", "hide": false, "legendFormat": "{{pod}}", "range": true, "refId": "A"}], "title": "Container Restarts", "type": "timeseries"}, {"datasource": {"type": "prometheus", "uid": "$datasource"}, "fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}, "custom": {"axisCenteredZero": false, "axisColorMode": "text", "axisLabel": "", "axisPlacement": "auto", "barAlignment": 0, "drawStyle": "line", "fillOpacity": 10, "gradientMode": "none", "hideFrom": {"legend": false, "tooltip": false, "viz": false}, "lineInterpolation": "linear", "lineStyle": {"fill": "solid"}, "lineWidth": 2, "pointSize": 5, "scaleDistribution": {"log": 2, "type": "log"}, "showPoints": "never", "spanNulls": false, "stacking": {"group": "A", "mode": "none"}, "thresholdsStyle": {"mode": "off"}}, "links": [], "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": null}]}, "unit": "short"}, "overrides": []}, "gridPos": {"h": 7, "w": 8, "x": 16, "y": 0}, "id": 124, "options": {"legend": {"calcs": [], "displayMode": "list", "placement": "bottom", "showLegend": true}, "tooltip": {"mode": "single", "sort": "none"}}, "panels": [], "pluginVersion": "9.2.6", "targets": [{"datasource": {"type": "prometheus", "uid": "$datasource"}, "editorMode": "code", "expr": "sum by(pod) (kube_pod_container_status_last_terminated_reason{reason=\"OOMKilled\", container=~\"compactor|query-frontend|querier|ingester|distributor|promtail\"}) > 0", "hide": false, "legendFormat": "{{pod}}", "range": true, "refId": "A"}], "title": "OOM Killed Pods", "type": "timeseries"}, {"datasource": {"type": "prometheus", "uid": "$datasource"}, "fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}, "custom": {"axisCenteredZero": false, "axisColorMode": "text", "axisLabel": "", "axisPlacement": "auto", "barAlignment": 0, "drawStyle": "line", "fillOpacity": 10, "gradientMode": "none", "hideFrom": {"legend": false, "tooltip": false, "viz": false}, "lineInterpolation": "linear", "lineStyle": {"fill": "solid"}, "lineWidth": 2, "pointSize": 5, "scaleDistribution": {"log": 2, "type": "log"}, "showPoints": "never", "spanNulls": false, "stacking": {"group": "A", "mode": "none"}, "thresholdsStyle": {"mode": "off"}}, "links": [], "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": null}, {"color": "red", "value": 70}]}, "unit": "short"}, "overrides": []}, "gridPos": {"h": 7, "w": 8, "x": 0, "y": 7}, "id": 119, "options": {"legend": {"calcs": [], "displayMode": "list", "placement": "bottom", "showLegend": true}, "tooltip": {"mode": "single", "sort": "none"}}, "panels": [], "pluginVersion": "9.2.6", "targets": [{"datasource": {"uid": "$datasource"}, "editorMode": "code", "expr": "sum(increase(container_cpu_cfs_throttled_periods_total{container=~\"promtail|distributor|ingester|querier|query-frontend\"}[5m])) by (container, pod, namespace) / sum(increase(container_cpu_cfs_periods_total{container=~\"promtail|distributor|ingester|querier|query-frontend\"}[5m])) by (container, pod, namespace) * 100 > 70", "legendFormat": "{{pod}}", "range": true, "refId": "A"}, {"datasource": {"type": "prometheus", "uid": "$datasource"}, "hide": false, "refId": "B"}], "title": "CPU Throttling", "type": "timeseries"}, {"datasource": {"type": "prometheus", "uid": "$datasource"}, "fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}, "custom": {"axisCenteredZero": false, "axisColorMode": "text", "axisLabel": "", "axisPlacement": "auto", "barAlignment": 0, "drawStyle": "line", "fillOpacity": 10, "gradientMode": "none", "hideFrom": {"legend": false, "tooltip": false, "viz": false}, "lineInterpolation": "linear", "lineStyle": {"fill": "solid"}, "lineWidth": 2, "pointSize": 5, "scaleDistribution": {"log": 2, "type": "log"}, "showPoints": "never", "spanNulls": false, "stacking": {"group": "A", "mode": "none"}, "thresholdsStyle": {"mode": "off"}}, "links": [], "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": null}, {"color": "red", "value": 70}]}, "unit": "short"}, "overrides": []}, "gridPos": {"h": 7, "w": 8, "x": 8, "y": 7}, "id": 121, "options": {"legend": {"calcs": [], "displayMode": "list", "placement": "bottom", "showLegend": true}, "tooltip": {"mode": "single", "sort": "none"}}, "panels": [], "pluginVersion": "9.2.6", "targets": [{"datasource": {"uid": "$datasource"}, "editorMode": "code", "expr": "sum(container_memory_working_set_bytes{container=~\"promtail|distributor|ingester|querier|query-frontend\"}) by (pod, container_name) / sum(container_spec_memory_limit_bytes{container=~\"promtail|distributor|ingester|querier|query-frontend\"}) by (pod, container_name) * 100 > 70", "legendFormat": "{{pod}}", "range": true, "refId": "A"}, {"datasource": {"type": "prometheus", "uid": "$datasource"}, "editorMode": "code", "expr": "", "hide": false, "legendFormat": "__auto", "range": true, "refId": "B"}], "title": "Memory Throttling", "type": "timeseries"}, {"datasource": {"type": "prometheus", "uid": "$datasource"}, "fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}, "custom": {"axisCenteredZero": false, "axisColorMode": "text", "axisLabel": "", "axisPlacement": "auto", "barAlignment": 0, "drawStyle": "line", "fillOpacity": 10, "gradientMode": "none", "hideFrom": {"legend": false, "tooltip": false, "viz": false}, "lineInterpolation": "linear", "lineStyle": {"fill": "solid"}, "lineWidth": 2, "pointSize": 5, "scaleDistribution": {"log": 2, "type": "log"}, "showPoints": "never", "spanNulls": false, "stacking": {"group": "A", "mode": "none"}, "thresholdsStyle": {"mode": "off"}}, "links": [], "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": null}]}, "unit": "s"}, "overrides": [{"__systemRef": "hideSeriesFrom", "matcher": {"id": "byNames", "options": {"mode": "exclude", "names": ["Average"], "prefix": "All except:", "readOnly": true}}, "properties": [{"id": "custom.hideFrom", "value": {"legend": false, "tooltip": false, "viz": true}}]}]}, "gridPos": {"h": 7, "w": 8, "x": 16, "y": 7}, "id": 118, "options": {"legend": {"calcs": [], "displayMode": "list", "placement": "bottom", "showLegend": true}, "tooltip": {"mode": "single", "sort": "none"}}, "panels": [], "pluginVersion": "9.2.6", "targets": [{"datasource": {"uid": "$datasource"}, "editorMode": "code", "expr": "histogram_quantile(0.99, sum(rate(promtail_request_duration_seconds_bucket[1m])) by (le, job))", "legendFormat": "99th Percentile", "range": true, "refId": "A"}, {"datasource": {"type": "prometheus", "uid": "$datasource"}, "editorMode": "code", "expr": "histogram_quantile(0.50, sum(rate(promtail_request_duration_seconds_bucket[1m])) by (le, job))", "hide": false, "legendFormat": "50th Percentile", "range": true, "refId": "B"}, {"datasource": {"type": "prometheus", "uid": "$datasource"}, "editorMode": "code", "expr": "sum(rate(promtail_request_duration_seconds_sum[1m])) by (job) / sum(rate(promtail_request_duration_seconds_count[1m])) by (job)", "hide": false, "legendFormat": "Average", "range": true, "refId": "C"}], "title": "Promtail Latency", "type": "timeseries"}, {"datasource": {"type": "prometheus", "uid": "$datasource"}, "fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}, "custom": {"axisCenteredZero": false, "axisColorMode": "text", "axisLabel": "", "axisPlacement": "auto", "barAlignment": 0, "drawStyle": "line", "fillOpacity": 10, "gradientMode": "none", "hideFrom": {"legend": false, "tooltip": false, "viz": false}, "lineInterpolation": "linear", "lineStyle": {"fill": "solid"}, "lineWidth": 2, "pointSize": 5, "scaleDistribution": {"log": 2, "type": "log"}, "showPoints": "never", "spanNulls": false, "stacking": {"group": "A", "mode": "none"}, "thresholdsStyle": {"mode": "off"}}, "links": [], "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": null}]}, "unit": "short"}, "overrides": []}, "gridPos": {"h": 7, "w": 8, "x": 0, "y": 14}, "id": 122, "options": {"legend": {"calcs": [], "displayMode": "list", "placement": "bottom", "showLegend": true}, "tooltip": {"mode": "single", "sort": "none"}}, "panels": [], "pluginVersion": "9.2.6", "targets": [{"datasource": {"type": "prometheus", "uid": "$datasource"}, "editorMode": "code", "expr": "increase(promtail_dropped_entries_total[1m]) > 0", "hide": false, "legendFormat": "{{pod}}", "range": true, "refId": "A"}], "title": "Log Entries Dropped", "type": "timeseries"}], "refresh": "10s", "schemaVersion": 37, "style": "dark", "tags": ["Facets", "loki"], "templating": {"list": [{"current": {"selected": false, "text": "Prometheus", "value": "Prometheus"}, "hide": 0, "includeAll": false, "label": "Data Source", "multi": false, "name": "datasource", "options": [], "query": "prometheus", "queryValue": "", "refresh": 1, "regex": "", "skipUrlSync": false, "type": "datasource"}]}, "time": {"from": "now-1h", "to": "now"}, "timepicker": {"refresh_intervals": ["5s", "10s", "30s", "1m", "5m", "15m", "30m", "1h", "2h", "1d"], "time_options": ["5m", "15m", "1h", "6h", "12h", "24h", "2d", "7d", "30d"]}, "timezone": "utc", "title": "<PERSON>", "uid": "userfriendly", "version": 1, "weekStart": ""}}, "advanced": {"inherit_from_base": true}, "out": {}}