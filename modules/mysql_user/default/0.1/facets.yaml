intent: mysql_user
flavor: default
version: '0.1'
description: Adds mysql_user - default flavor
clouds:
- aws
- gcp
- azure
- kubernetes
sample:
  $schema: https://facets-cloud.github.io/facets-schemas/schemas/mysql_user/mysql_user.schema.json
  kind: mysql_user
  flavor: default
  version: '0.1'
  metadata: {}
  spec:
    endpoint: mysql:<EMAIL>:3306
    permissions:
      permission1:
        permission: RO
        database: my_database
        table: '*'
      permission2:
        permission: ADMIN
        database: '*'
        table: '*'
  disabled: true
