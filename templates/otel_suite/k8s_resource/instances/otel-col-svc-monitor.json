{"kind": "k8s_resource", "metadata": {}, "flavor": "k8s", "version": "0.3", "disabled": true, "spec": {"resource": {"apiVersion": "monitoring.coreos.com/v1", "kind": "ServiceMonitor", "metadata": {"labels": {"app": "otel-collector", "app.kubernetes.io/instance": "otel-collector", "app.kubernetes.io/name": "otel-collector"}, "name": "otel-collector", "namespace": "otel-operator"}, "spec": {"endpoints": [{"honorLabels": false, "interval": "60s", "path": "/metrics", "scrapeTimeout": "30s", "targetPort": 9090}], "jobLabel": "otel-collector", "selector": {"matchLabels": {"app.kubernetes.io/component": "opentelemetry-collector", "app.kubernetes.io/managed-by": "opentelemetry-operator", "app.kubernetes.io/name": "otel-operator-daemon-collector", "operator.opentelemetry.io/collector-service-type": "base"}}}}}}