{"$schema": "https://facets-cloud.github.io/facets-schemas/schemas/grafana_dashboard/grafana_dashboard.schema.json", "kind": "grafana_dashboard", "flavor": "default", "version": "latest", "lifecycle": "ENVIRONMENT", "disabled": false, "provided": false, "depends_on": [], "metadata": {"name": "node-resource-summary"}, "ui": {"base_resource": true}, "spec": {"dashboard": {"__inputs": [], "__elements": {}, "__requires": [{"type": "grafana", "id": "grafana", "name": "<PERSON><PERSON>", "version": "9.2.15"}, {"type": "datasource", "id": "prometheus", "name": "Prometheus", "version": "1.0.0"}, {"type": "panel", "id": "stat", "name": "Stat", "version": ""}, {"type": "panel", "id": "table", "name": "Table", "version": ""}], "annotations": {"list": [{"builtIn": 1, "datasource": {"type": "grafana", "uid": "-- <PERSON><PERSON> --"}, "enable": true, "hide": true, "iconColor": "rgba(0, 211, 255, 1)", "name": "Annotations & Alerts", "target": {"limit": 100, "matchAny": false, "tags": [], "type": "dashboard"}, "type": "dashboard"}]}, "editable": true, "fiscalYearStartMonth": 0, "graphTooltip": 0, "links": [], "liveNow": false, "panels": [{"datasource": {"type": "prometheus", "uid": "prometheus"}, "description": "", "fieldConfig": {"defaults": {"color": {"mode": "thresholds"}, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green"}]}}, "overrides": [{"matcher": {"id": "by<PERSON><PERSON>", "options": "Value #A"}, "properties": [{"id": "displayName", "value": "Total Allocatable CPU"}]}, {"matcher": {"id": "by<PERSON><PERSON>", "options": "Value #B"}, "properties": [{"id": "displayName", "value": "Allocated CPU"}]}, {"matcher": {"id": "by<PERSON><PERSON>", "options": "Value #B / Value #A"}, "properties": [{"id": "unit", "value": "percentunit"}, {"id": "displayName", "value": "CPU Allocation"}, {"id": "thresholds", "value": {"mode": "absolute", "steps": [{"color": "red"}, {"color": "yellow", "value": 0.4}, {"color": "green", "value": 0.6}]}}]}]}, "gridPos": {"h": 8, "w": 12, "x": 0, "y": 0}, "id": 4, "options": {"colorMode": "value", "graphMode": "area", "justifyMode": "center", "orientation": "auto", "reduceOptions": {"calcs": ["lastNotNull"], "fields": "", "values": false}, "text": {"titleSize": 15}, "textMode": "value_and_name"}, "pluginVersion": "9.2.15", "targets": [{"datasource": {"type": "prometheus", "uid": "prometheus"}, "editorMode": "code", "exemplar": false, "expr": "sum(kube_node_status_allocatable{resource=\"cpu\"})", "format": "table", "instant": true, "legendFormat": "__auto", "range": false, "refId": "A"}, {"datasource": {"type": "prometheus", "uid": "prometheus"}, "editorMode": "code", "exemplar": false, "expr": "sum(kube_pod_container_resource_requests{resource=\"cpu\"})", "format": "table", "hide": false, "instant": true, "legendFormat": "__auto", "range": false, "refId": "B"}], "title": "Cluster CPU Allocation Summary", "transformations": [{"id": "calculateField", "options": {"binary": {"left": "Value #B", "operator": "/", "reducer": "sum", "right": "Value #A"}, "mode": "binary", "reduce": {"reducer": "sum"}}}, {"id": "organize", "options": {"excludeByName": {}, "indexByName": {"Time": 0, "Value #A": 1, "Value #B": 2, "Value #B / Value #A": 3, "Value #C": 4, "Value #D": 5, "Value #D / Value #C": 6}, "renameByName": {}}}], "type": "stat"}, {"datasource": {"type": "prometheus", "uid": "prometheus"}, "description": "", "fieldConfig": {"defaults": {"color": {"mode": "thresholds"}, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green"}]}}, "overrides": [{"matcher": {"id": "by<PERSON><PERSON>", "options": "Value #A"}, "properties": [{"id": "displayName", "value": "Total Allocatable Memory"}, {"id": "unit", "value": "decbytes"}]}, {"matcher": {"id": "by<PERSON><PERSON>", "options": "Value #B"}, "properties": [{"id": "displayName", "value": "Allocated Memory"}, {"id": "unit", "value": "decbytes"}]}, {"matcher": {"id": "by<PERSON><PERSON>", "options": "Value #C"}, "properties": [{"id": "displayName", "value": "Total Allocatable Memory"}, {"id": "unit", "value": "decbytes"}]}, {"matcher": {"id": "by<PERSON><PERSON>", "options": "Value #B / Value #A"}, "properties": [{"id": "unit", "value": "percentunit"}, {"id": "displayName", "value": "Memory Allocation"}, {"id": "thresholds", "value": {"mode": "absolute", "steps": [{"color": "red"}, {"color": "yellow", "value": 0.4}, {"color": "green", "value": 0.6}]}}]}]}, "gridPos": {"h": 8, "w": 12, "x": 12, "y": 0}, "id": 5, "options": {"colorMode": "value", "graphMode": "area", "justifyMode": "center", "orientation": "auto", "reduceOptions": {"calcs": ["lastNotNull"], "fields": "", "values": false}, "text": {"titleSize": 15}, "textMode": "value_and_name"}, "pluginVersion": "9.2.15", "targets": [{"datasource": {"type": "prometheus", "uid": "prometheus"}, "editorMode": "code", "exemplar": false, "expr": "sum(kube_node_status_allocatable{resource=\"memory\"})", "format": "table", "instant": true, "legendFormat": "__auto", "range": false, "refId": "A"}, {"datasource": {"type": "prometheus", "uid": "prometheus"}, "editorMode": "code", "exemplar": false, "expr": "sum(kube_pod_container_resource_requests{resource=\"memory\"})", "format": "table", "hide": false, "instant": true, "legendFormat": "__auto", "range": false, "refId": "B"}], "title": "Cluster Memory Allocation Summary", "transformations": [{"id": "calculateField", "options": {"binary": {"left": "Value #B", "operator": "/", "reducer": "sum", "right": "Value #A"}, "mode": "binary", "reduce": {"reducer": "sum"}}}, {"id": "organize", "options": {"excludeByName": {}, "indexByName": {"Time": 0, "Value #A": 1, "Value #B": 2, "Value #B / Value #A": 3, "Value #C": 4, "Value #D": 5, "Value #D / Value #C": 6}, "renameByName": {}}}], "type": "stat"}, {"datasource": {"type": "prometheus", "uid": "prometheus"}, "fieldConfig": {"defaults": {"color": {"mode": "thresholds"}, "custom": {"align": "center", "displayMode": "auto", "inspect": false}, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green"}]}}, "overrides": [{"matcher": {"id": "by<PERSON><PERSON>", "options": "Value #A / Value #C"}, "properties": [{"id": "custom.width", "value": 200}, {"id": "unit", "value": "percentunit"}, {"id": "displayName", "value": "CPU Allocation"}]}, {"matcher": {"id": "by<PERSON><PERSON>", "options": "Value #C"}, "properties": [{"id": "custom.width", "value": 179}]}, {"matcher": {"id": "by<PERSON><PERSON>", "options": "node"}, "properties": [{"id": "custom.width", "value": 300}]}, {"matcher": {"id": "by<PERSON><PERSON>", "options": "Value #C"}, "properties": [{"id": "displayName", "value": "Total CPU Capacity"}, {"id": "custom.hidden", "value": true}]}, {"matcher": {"id": "by<PERSON><PERSON>", "options": "Value #A"}, "properties": [{"id": "displayName", "value": "Total CPU Requests"}]}, {"matcher": {"id": "by<PERSON><PERSON>", "options": "Value #B"}, "properties": [{"id": "displayName", "value": "Allocatable CPU"}]}, {"matcher": {"id": "by<PERSON><PERSON>", "options": "Value #D"}, "properties": [{"id": "unit", "value": "decbytes"}, {"id": "displayName", "value": "Memory Requests"}]}, {"matcher": {"id": "by<PERSON><PERSON>", "options": "Value #E"}, "properties": [{"id": "unit", "value": "decbytes"}, {"id": "displayName", "value": "Allocatable Memory"}]}, {"matcher": {"id": "by<PERSON><PERSON>", "options": "Value #F"}, "properties": [{"id": "unit", "value": "decbytes"}, {"id": "displayName", "value": "Total Memory Capacity"}, {"id": "custom.hidden", "value": true}]}, {"matcher": {"id": "by<PERSON><PERSON>", "options": "Total Memory Capacity"}, "properties": [{"id": "custom.width", "value": 175}]}, {"matcher": {"id": "by<PERSON><PERSON>", "options": "Value #D / Value #F"}, "properties": [{"id": "unit", "value": "percentunit"}, {"id": "displayName", "value": "Memory Allocation"}]}, {"matcher": {"id": "by<PERSON><PERSON>", "options": "node"}, "properties": [{"id": "links", "value": [{"targetBlank": true, "title": "", "url": "/d/p4lLcnDIz/node-resource-allocation?orgId=1&var-node=$${__value.raw}"}]}]}]}, "gridPos": {"h": 12, "w": 24, "x": 0, "y": 8}, "id": 2, "options": {"footer": {"enablePagination": true, "fields": "", "reducer": ["sum"], "show": false}, "showHeader": true, "sortBy": []}, "pluginVersion": "9.2.15", "targets": [{"datasource": {"type": "prometheus", "uid": "prometheus"}, "editorMode": "code", "exemplar": false, "expr": "sum(kube_pod_container_resource_requests{resource=\"cpu\"}) by (node)", "format": "table", "instant": true, "legendFormat": "__auto", "range": false, "refId": "A"}, {"datasource": {"type": "prometheus", "uid": "prometheus"}, "editorMode": "code", "exemplar": false, "expr": "kube_node_status_allocatable{resource=\"cpu\"}", "format": "table", "hide": false, "instant": true, "legendFormat": "__auto", "range": false, "refId": "B"}, {"datasource": {"type": "prometheus", "uid": "prometheus"}, "editorMode": "code", "exemplar": false, "expr": "kube_node_status_allocatable{resource=\"cpu\"}", "format": "table", "hide": false, "instant": true, "legendFormat": "__auto", "range": false, "refId": "C"}, {"datasource": {"type": "prometheus", "uid": "prometheus"}, "editorMode": "code", "exemplar": false, "expr": "sum(kube_pod_container_resource_requests{resource=\"memory\"}) by (node)", "format": "table", "hide": false, "instant": true, "legendFormat": "__auto", "range": false, "refId": "D"}, {"datasource": {"type": "prometheus", "uid": "prometheus"}, "editorMode": "code", "exemplar": false, "expr": "kube_node_status_allocatable{resource=\"memory\"}", "format": "table", "hide": false, "instant": true, "legendFormat": "__auto", "range": false, "refId": "E"}, {"datasource": {"type": "prometheus", "uid": "prometheus"}, "editorMode": "code", "exemplar": false, "expr": "kube_node_status_allocatable{resource=\"memory\"}", "format": "table", "hide": false, "instant": true, "legendFormat": "__auto", "range": false, "refId": "F"}], "title": "Node Resource Allocation", "transformations": [{"id": "joinByField", "options": {"byField": "node", "mode": "inner"}}, {"id": "filterFieldsByName", "options": {"include": {"names": ["node", "Value #A", "Value #B", "Value #C", "Value #D", "Value #E", "Value #F"]}}}, {"id": "calculateField", "options": {"binary": {"left": "Value #A", "operator": "/", "reducer": "sum", "right": "Value #C"}, "mode": "binary", "reduce": {"reducer": "sum"}}}, {"id": "calculateField", "options": {"binary": {"left": "Value #D", "operator": "/", "reducer": "sum", "right": "Value #F"}, "mode": "binary", "reduce": {"reducer": "sum"}}}], "type": "table"}], "schemaVersion": 37, "style": "dark", "tags": [], "templating": {"list": []}, "time": {"from": "now-6h", "to": "now"}, "timepicker": {}, "timezone": "", "title": "Node Resource Allocation Summary", "uid": "ntyvS7vSk", "version": 11, "weekStart": ""}}, "out": {}, "advanced": {"inherit_from_base": true, "default": {"preserve_uid": true}}}