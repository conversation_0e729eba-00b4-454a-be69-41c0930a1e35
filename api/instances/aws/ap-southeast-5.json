{"16Cores": {"onDemand": [{"Architecture": ["x86_64"], "BestOnDemandHourPriceRegion": "ap-southeast-5", "CanonicalName": "c6i.4xlarge", "InstanceType": "c6i.4xlarge", "Memory": 32768, "ModifiedDate": "2024-11-13 00:29:56", "PaymentType": "OnDemand", "PricePerHour": 0.6664, "tags": ["cheapest"]}, {"Architecture": ["x86_64"], "BestOnDemandHourPriceRegion": "ap-southeast-5", "CanonicalName": "c7i.4xlarge", "InstanceType": "c7i.4xlarge", "Memory": 32768, "ModifiedDate": "2024-11-13 00:29:56", "PaymentType": "OnDemand", "PricePerHour": 0.69976, "tags": []}, {"Architecture": ["x86_64"], "BestOnDemandHourPriceRegion": "ap-southeast-5", "CanonicalName": "c6id.4xlarge", "InstanceType": "c6id.4xlarge", "Memory": 32768, "ModifiedDate": "2024-11-13 00:29:56", "PaymentType": "OnDemand", "PricePerHour": 0.79968, "tags": []}, {"Architecture": ["x86_64"], "BestOnDemandHourPriceRegion": "us-east-1", "CanonicalName": "m6i.4xlarge", "InstanceType": "m6i.4xlarge", "Memory": 65536, "ModifiedDate": "2024-11-13 00:29:56", "PaymentType": "OnDemand", "PricePerHour": 0.816, "tags": []}], "spot": []}, "4Cores": {"onDemand": [{"Architecture": ["x86_64"], "BestOnDemandHourPriceRegion": "ap-southeast-5", "CanonicalName": "c6i.xlarge", "InstanceType": "c6i.xlarge", "Memory": 8192, "ModifiedDate": "2024-11-13 00:29:56", "PaymentType": "OnDemand", "PricePerHour": 0.1666, "tags": ["cheapest"]}, {"Architecture": ["x86_64"], "BestOnDemandHourPriceRegion": "ap-southeast-5", "CanonicalName": "c7i.xlarge", "InstanceType": "c7i.xlarge", "Memory": 8192, "ModifiedDate": "2024-11-13 00:29:56", "PaymentType": "OnDemand", "PricePerHour": 0.17494, "tags": []}, {"Architecture": ["x86_64"], "BestOnDemandHourPriceRegion": "us-east-1", "CanonicalName": "t3.xlarge", "InstanceType": "t3.xlarge", "Memory": 16384, "ModifiedDate": "2024-11-13 00:29:56", "PaymentType": "OnDemand", "PricePerHour": 0.1901, "tags": []}, {"Architecture": ["x86_64"], "BestOnDemandHourPriceRegion": "ap-southeast-5", "CanonicalName": "c6id.xlarge", "InstanceType": "c6id.xlarge", "Memory": 8192, "ModifiedDate": "2024-11-13 00:29:56", "PaymentType": "OnDemand", "PricePerHour": 0.19992, "tags": []}], "spot": []}, "8Cores": {"onDemand": [{"Architecture": ["x86_64"], "BestOnDemandHourPriceRegion": "ap-southeast-5", "CanonicalName": "c6i.2xlarge", "InstanceType": "c6i.2xlarge", "Memory": 16384, "ModifiedDate": "2024-11-13 00:29:56", "PaymentType": "OnDemand", "PricePerHour": 0.3332, "tags": ["cheapest"]}, {"Architecture": ["x86_64"], "BestOnDemandHourPriceRegion": "ap-southeast-5", "CanonicalName": "c7i.2xlarge", "InstanceType": "c7i.2xlarge", "Memory": 16384, "ModifiedDate": "2024-11-13 00:29:56", "PaymentType": "OnDemand", "PricePerHour": 0.34988, "tags": []}, {"Architecture": ["x86_64"], "BestOnDemandHourPriceRegion": "us-east-1", "CanonicalName": "t3.2xlarge", "InstanceType": "t3.2xlarge", "Memory": 32768, "ModifiedDate": "2024-11-13 00:29:56", "PaymentType": "OnDemand", "PricePerHour": 0.3802, "tags": []}, {"Architecture": ["x86_64"], "BestOnDemandHourPriceRegion": "ap-southeast-5", "CanonicalName": "c6id.2xlarge", "InstanceType": "c6id.2xlarge", "Memory": 16384, "ModifiedDate": "2024-11-13 00:29:56", "PaymentType": "OnDemand", "PricePerHour": 0.39984, "tags": []}], "spot": []}}