intent: cassandra
flavor: k8s
version: "0.1"
description: Adds cassandra - k8s flavor
clouds:
  - aws
  - gcp
  - azure
  - kubernetes
metadata:
  title: Metadata of Cassandra
  type: object
  properties:
    namespace:
      type: string
      title: Namespace
      description: Namespace in which <PERSON> should be deployed
      default: default
spec:
  title: K8s Cassandra
  type: object
  properties:
    cassandra_version:
      type: string
      title: Cassandra Version
      description: Version of Cassandra DB
      minLength: 1
      enum:
        - "5.0.2"
        - "4.1.7"
        - "4.0.15"
    size:
      type: object
      title: Size
      description: Datastore Sizing
      properties:
        cpu:
          type: string
          title: CPU
          description: Number of CPU cores required
          minLength: 1
          pattern: "^[0-9]+[m]?$"
          x-ui-compare:
            field: spec.size.cpu_limit
            comparator: "<="
            x-ui-error-message: "CPU cannot be more than CPU limit"
          x-ui-placeholder: "Enter CPU cores"
          x-ui-error-message: "Value doesn't match '^[0-9]+[m]?$' pattern"
        memory:
          type: string
          title: Memory
          description: Amount of required memory
          minLength: 1
          pattern: "^[0-9]+(\\.[0-9]+)?[EiKMGTP]i?$"
          x-ui-compare:
            field: spec.size.memory_limit
            comparator: "<="
            x-ui-error-message: "Memory cannot be more than memory limit"
          x-ui-placeholder: "Enter memory (Ex. 2Gi)"
          x-ui-error-message: "Value doesn't match '^[0-9]+(\\.[0-9]+)?[EiKMGTP]i?' pattern"
        cpu_limit:
          type: string
          title: CPU Limit
          description: CPU limit for the instance
          minLength: 1
          pattern: "^[0-9]+[m]?$"
          x-ui-compare:
            field: spec.size.cpu
            comparator: ">="
            x-ui-error-message: "CPU limit cannot be less than CPU"
          x-ui-placeholder: "Enter CPU limit"
          x-ui-error-message: "Value doesn't match '^[0-9]+[m]?$' pattern"
        memory_limit:
          type: string
          title: Memory Limit
          description: Memory limit for the instance
          minLength: 1
          pattern: "^[0-9]+(\\.[0-9]+)?[EiKMGTP]i?$"
          x-ui-compare:
            field: spec.size.memory
            comparator: ">="
            x-ui-error-message: "Memory limit cannot be less than memory"
          x-ui-placeholder: "Enter memory limit"
          x-ui-error-message: "Value doesn't match '^[0-9]+(\\.[0-9]+)?[EiKMGTP]i?' pattern"
        volume:
          type: string
          title: Volume
          description: Size of the volume
          pattern: "^[0-9]+[G]i?$"
          x-ui-placeholder: "Enter volume size"
          x-ui-error-message: "Value doesn't match '^[0-9]+[G]i?$' pattern"
        instance_count:
          type: integer
          title: Instance Count
          description: Number of instances to create
          minimum: 1
          maximum: 100
          x-ui-placeholder: "Enter instance count"
      required:
        - cpu
        - memory
        - volume
        - instance_count
  required:
    - size
    - cassandra_version
  x-ui-order:
    - cassandra_version
    - size
sample:
  $schema: https://facets-cloud.github.io/facets-schemas/schemas/cassandra/cassandra.schema.json
  flavor: k8s
  kind: cassandra
  version: "0.1"
  metadata: {}
  spec:
    cassandra_version: 5.0.2
    size:
      cpu: "2"
      memory: 2Gi
      volume: 5Gi
      replica_count: 1
  disabled: true
