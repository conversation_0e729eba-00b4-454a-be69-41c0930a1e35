intent: mysql
flavor: k8s
version: "0.2"
description: Adds MYSQL module of flavor kubernetes
clouds:
  - aws
  - azure
  - gcp
  - kubernetes
metadata:
  title: Metadata of Mysql
  type: object
  properties:
    namespace:
      type: string
      title: Namespace
      description: Namespace in which Mysql should be deployed
      default: default
spec:
  title: K8s
  type: object
  properties:
    mysql_version:
      type: string
      title: MySQL Version
      description: Version of MySQL
      enum:
        - "8.0.34"
        - "8.0.4"
        - "8.4.3"
        - "9.0.1"
      x-ui-placeholder: "Ex. 8.4"
    size:
      type: object
      title: Size
      description: Writer and Reader Datastore Sizing
      properties:
        writer:
          type: object
          title: Writer
          description: Writer Node Configuration
          properties:
            cpu:
              type: string
              title: CPU
              description: Number of CPU Cores
              minLength: 1
              pattern: "^([1-9]|[12][0-9]|3[0-2])$|^(3[0-1][0-9]{3}|[1-2][0-9]{4}|[1-9][0-9]{0,3}|32000)m$"
              x-ui-compare:
                field: spec.size.writer.cpu_limit
                comparator: "<="
                x-ui-error-message: "CPU cannot be more than cpu limit"
              x-ui-placeholder: "e.g., '500m' or '1'"
              x-ui-error-message: "Value doesn't match pattern, it should be number ranging from 1 to 32 or 1m to 32000m"
            memory:
              type: string
              title: Memory
              description: Amount of memory
              minLength: 1
              pattern: "^([1-9]|[1-5][0-9]|6[0-4])Gi$|^([1-9]|[1-9][0-9]{1,3}|[1-5][0-9]{4}|6[0-3][0-9]{3}|64000)Mi$"
              x-ui-compare:
                field: spec.size.writer.memory_limit
                comparator: "<="
                x-ui-error-message: "Memory cannot be more than memory limit"
              x-ui-placeholder: "e.g., '800Mi' or '1.5Gi'"
              x-ui-error-message: "Value doesn't match pattern, it should be number ranging from 1Gi to 64Gi or 1Mi to 64000Mi"
            cpu_limit:
              type: string
              title: CPU Limit
              description: CPU limit
              minLength: 1
              pattern: "^([1-9]|[12][0-9]|3[0-2])$|^(3[0-1][0-9]{3}|[1-2][0-9]{4}|[1-9][0-9]{0,3}|32000)m$"
              x-ui-compare:
                field: spec.size.writer.cpu
                comparator: ">="
                x-ui-error-message: "CPU limit cannot be less than cpu"
              x-ui-placeholder: "e.g., '500m' or '1'"
              x-ui-error-message: "Value doesn't match pattern, it should be number ranging from 1 to 32 or 1m to 32000m"
            memory_limit:
              type: string
              title: Memory Limit
              description: Memory limit
              minLength: 1
              pattern: "^([1-9]|[1-5][0-9]|6[0-4])Gi$|^([1-9]|[1-9][0-9]{1,3}|[1-5][0-9]{4}|6[0-3][0-9]{3}|64000)Mi$"
              x-ui-compare:
                field: spec.size.writer.memory
                comparator: ">="
                x-ui-error-message: "Memory limit cannot be less than memory"
              x-ui-placeholder: "e.g., '800Mi' or '1.5Gi'"
              x-ui-error-message: "Value doesn't match pattern, it should be number ranging from 1Gi to 64Gi or 1Mi to 64000Mi"
            volume:
              type: string
              title: Volume
              description: Volume Size
              pattern: "^[0-9]+(\\.[0-9]+)?[EiKMGTP]i?$"
              x-ui-placeholder: "e.g., '10Gi' or '50Gi'"
              x-ui-error-message: "Volume must be specified in the correct format (e.g., '10Gi' or '50Gi')."
          required:
            - cpu
            - memory
            - volume
        reader:
          type: object
          title: Reader
          description: Reader Node Configuration
          properties:
            cpu:
              type: string
              title: CPU
              description: Number of CPU cores
              minLength: 1
              pattern: "^([1-9]|[12][0-9]|3[0-2])$|^(3[0-1][0-9]{3}|[1-2][0-9]{4}|[1-9][0-9]{0,3}|32000)m$"
              x-ui-compare:
                field: spec.size.reader.cpu_limit
                comparator: "<="
                x-ui-error-message: "CPU cannot be more than cpu limit"
              x-ui-placeholder: "e.g., '500m' or '1'"
              x-ui-error-message: "Value doesn't match pattern, it should be number ranging from 1 to 32 or 1m to 32000m"
            memory:
              type: string
              title: Memory
              description: Amount of memory
              minLength: 1
              pattern: "^([1-9]|[1-5][0-9]|6[0-4])Gi$|^([1-9]|[1-9][0-9]{1,3}|[1-5][0-9]{4}|6[0-3][0-9]{3}|64000)Mi$"
              x-ui-compare:
                field: spec.size.reader.memory_limit
                comparator: "<="
                x-ui-error-message: "Memory cannot be more than memory limit"
              x-ui-placeholder: "e.g., '800Mi' or '1.5Gi'"
              x-ui-error-message: "Value doesn't match pattern, it should be number ranging from 1Gi to 64Gi or 1Mi to 64000Mi"
            cpu_limit:
              type: string
              title: CPU Limit
              description: CPU limit
              minLength: 1
              pattern: "^([1-9]|[12][0-9]|3[0-2])$|^(3[0-1][0-9]{3}|[1-2][0-9]{4}|[1-9][0-9]{0,3}|32000)m$"
              x-ui-compare:
                field: spec.size.reader.cpu
                comparator: ">="
                x-ui-error-message: "CPU limit cannot be less than cpu"
              x-ui-placeholder: "e.g., '500m' or '1'"
              x-ui-error-message: "Value doesn't match pattern, it should be number ranging from 1 to 32 or 1m to 32000m"
            memory_limit:
              type: string
              title: Memory Limit
              description: Memory limit
              minLength: 1
              pattern: "^([1-9]|[1-5][0-9]|6[0-4])Gi$|^([1-9]|[1-9][0-9]{1,3}|[1-5][0-9]{4}|6[0-3][0-9]{3}|64000)Mi$"
              x-ui-compare:
                field: spec.size.reader.memory
                comparator: ">="
                x-ui-error-message: "Memory limit cannot be less than memory"
              x-ui-placeholder: "e.g., '800Mi' or '1.5Gi'"
              x-ui-error-message: "Value doesn't match pattern, it should be number ranging from 1Gi to 64Gi or 1Mi to 64000Mi"
            volume:
              type: string
              title: Volume
              description: Volume size
              pattern: "^[0-9]+(\\.[0-9]+)?[EiKMGTP]i?"
              x-ui-placeholder: "Ex. 8Gi"
              x-ui-error-message: "Value doesn't match '^[0-9]+(\\.[0-9]+)?[EiKMGTP]i?$' pattern"
            instance_count:
              type: integer
              title: Instance Count
              description: Number of Reader Instances
              x-ui-placeholder: "Enter reader instance count"
              minimum: 0
  required:
    - size
    - mysql_version
  x-ui-order:
    - mysql_version
    - size
  x-ui-error-message: "Please provide required information for the size."
sample:
  $schema: "https://facets-cloud.github.io/facets-schemas/schemas/mysql/mysql.schema.json"
  flavor: k8s
  kind: mysql
  metadata: {}
  version: "0.2"
  disabled: true
  spec:
    mysql_version: 8.4.3
    size:
      writer:
        cpu: 1
        memory: 1Gi
        cpu_limit: 1
        memory_limit: 1Gi
        volume: 8G
