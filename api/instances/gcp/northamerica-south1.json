{"16Cores": {"onDemand": [{"Architecture": "x86_64", "BestOnDemandHourPriceRegion": "us-central1", "CanonicalName": "e2-highcpu-16", "InstanceType": "e2-highcpu-16", "Memory": 16384, "ModifiedDate": "2024-11-13 00:01:25", "PaymentType": "OnDemand", "PricePerHour": 0.431380448, "tags": ["cheapest"]}, {"Architecture": "x86_64", "BestOnDemandHourPriceRegion": "us-central1", "CanonicalName": "e2-standard-16", "InstanceType": "e2-standard-16", "Memory": 65536, "ModifiedDate": "2024-11-13 00:01:25", "PaymentType": "OnDemand", "PricePerHour": 0.584339552, "tags": []}, {"Architecture": "x86_64", "BestOnDemandHourPriceRegion": "us-central1", "CanonicalName": "n4-highcpu-16", "InstanceType": "n4-highcpu-16", "Memory": 32768, "ModifiedDate": "2024-11-13 00:01:25", "PaymentType": "OnDemand", "PricePerHour": 0.69728608, "tags": []}, {"Architecture": "x86_64", "BestOnDemandHourPriceRegion": "us-central1", "CanonicalName": "c4-highcpu-16", "InstanceType": "c4-highcpu-16", "Memory": 32768, "ModifiedDate": "2024-11-13 00:01:25", "PaymentType": "OnDemand", "PricePerHour": 0.7416534400000001, "tags": []}], "spot": [{"Architecture": "x86_64", "BestOnDemandHourPriceRegion": "us-central1", "CanonicalName": "e2-highcpu-16", "InstanceType": "e2-highcpu-16", "Memory": 16384, "ModifiedDate": "2024-11-13 00:01:25", "PaymentType": "OnDemand", "PricePerHour": 0.15624496000000002, "tags": ["cheapest"]}, {"Architecture": "x86_64", "BestOnDemandHourPriceRegion": "us-central1", "CanonicalName": "e2-standard-16", "InstanceType": "e2-standard-16", "Memory": 65536, "ModifiedDate": "2024-11-13 00:01:25", "PaymentType": "OnDemand", "PricePerHour": 0.21165184, "tags": []}, {"Architecture": "x86_64", "BestOnDemandHourPriceRegion": "us-central1", "CanonicalName": "n4-highcpu-16", "InstanceType": "n4-highcpu-16", "Memory": 32768, "ModifiedDate": "2024-11-13 00:01:25", "PaymentType": "OnDemand", "PricePerHour": 0.278914464, "tags": []}, {"Architecture": "x86_64", "BestOnDemandHourPriceRegion": "us-central1", "CanonicalName": "e2-highmem-16", "InstanceType": "e2-highmem-16", "Memory": 131072, "ModifiedDate": "2024-11-13 00:01:25", "PaymentType": "OnDemand", "PricePerHour": 0.28552768, "tags": []}]}, "4Cores": {"onDemand": [{"Architecture": "x86_64", "BestOnDemandHourPriceRegion": "us-central1", "CanonicalName": "e2-highcpu-4", "InstanceType": "e2-highcpu-4", "Memory": 4096, "ModifiedDate": "2024-11-13 00:01:25", "PaymentType": "OnDemand", "PricePerHour": 0.107845112, "tags": ["cheapest"]}, {"Architecture": "x86_64", "BestOnDemandHourPriceRegion": "us-central1", "CanonicalName": "e2-standard-4", "InstanceType": "e2-standard-4", "Memory": 16384, "ModifiedDate": "2024-11-13 00:01:25", "PaymentType": "OnDemand", "PricePerHour": 0.146084888, "tags": []}, {"Architecture": "x86_64", "BestOnDemandHourPriceRegion": "us-central1", "CanonicalName": "n4-highcpu-4", "InstanceType": "n4-highcpu-4", "Memory": 8192, "ModifiedDate": "2024-11-13 00:01:25", "PaymentType": "OnDemand", "PricePerHour": 0.17432152, "tags": []}, {"Architecture": "x86_64", "BestOnDemandHourPriceRegion": "us-central1", "CanonicalName": "c4-highcpu-4", "InstanceType": "c4-highcpu-4", "Memory": 8192, "ModifiedDate": "2024-11-13 00:01:25", "PaymentType": "OnDemand", "PricePerHour": 0.18541336000000003, "tags": []}], "spot": [{"Architecture": "x86_64", "BestOnDemandHourPriceRegion": "us-central1", "CanonicalName": "e2-highcpu-4", "InstanceType": "e2-highcpu-4", "Memory": 4096, "ModifiedDate": "2024-11-13 00:01:25", "PaymentType": "OnDemand", "PricePerHour": 0.039061240000000004, "tags": ["cheapest"]}, {"Architecture": "x86_64", "BestOnDemandHourPriceRegion": "us-central1", "CanonicalName": "e2-standard-4", "InstanceType": "e2-standard-4", "Memory": 16384, "ModifiedDate": "2024-11-13 00:01:25", "PaymentType": "OnDemand", "PricePerHour": 0.05291296, "tags": []}, {"Architecture": "x86_64", "BestOnDemandHourPriceRegion": "us-central1", "CanonicalName": "n4-highcpu-4", "InstanceType": "n4-highcpu-4", "Memory": 8192, "ModifiedDate": "2024-11-13 00:01:25", "PaymentType": "OnDemand", "PricePerHour": 0.069728616, "tags": []}, {"Architecture": "x86_64", "BestOnDemandHourPriceRegion": "us-central1", "CanonicalName": "e2-highmem-4", "InstanceType": "e2-highmem-4", "Memory": 32768, "ModifiedDate": "2024-11-13 00:01:25", "PaymentType": "OnDemand", "PricePerHour": 0.07138192, "tags": []}]}, "8Cores": {"onDemand": [{"Architecture": "x86_64", "BestOnDemandHourPriceRegion": "us-central1", "CanonicalName": "e2-highcpu-8", "InstanceType": "e2-highcpu-8", "Memory": 8192, "ModifiedDate": "2024-11-13 00:01:25", "PaymentType": "OnDemand", "PricePerHour": 0.215690224, "tags": ["cheapest"]}, {"Architecture": "x86_64", "BestOnDemandHourPriceRegion": "us-central1", "CanonicalName": "e2-standard-8", "InstanceType": "e2-standard-8", "Memory": 32768, "ModifiedDate": "2024-11-13 00:01:25", "PaymentType": "OnDemand", "PricePerHour": 0.292169776, "tags": []}, {"Architecture": "x86_64", "BestOnDemandHourPriceRegion": "us-central1", "CanonicalName": "n4-highcpu-8", "InstanceType": "n4-highcpu-8", "Memory": 16384, "ModifiedDate": "2024-11-13 00:01:25", "PaymentType": "OnDemand", "PricePerHour": 0.34864304, "tags": []}, {"Architecture": "x86_64", "BestOnDemandHourPriceRegion": "us-central1", "CanonicalName": "c4-highcpu-8", "InstanceType": "c4-highcpu-8", "Memory": 16384, "ModifiedDate": "2024-11-13 00:01:25", "PaymentType": "OnDemand", "PricePerHour": 0.37082672000000005, "tags": []}], "spot": [{"Architecture": "x86_64", "BestOnDemandHourPriceRegion": "us-central1", "CanonicalName": "e2-highcpu-8", "InstanceType": "e2-highcpu-8", "Memory": 8192, "ModifiedDate": "2024-11-13 00:01:25", "PaymentType": "OnDemand", "PricePerHour": 0.07812248000000001, "tags": ["cheapest"]}, {"Architecture": "x86_64", "BestOnDemandHourPriceRegion": "us-central1", "CanonicalName": "e2-standard-8", "InstanceType": "e2-standard-8", "Memory": 32768, "ModifiedDate": "2024-11-13 00:01:25", "PaymentType": "OnDemand", "PricePerHour": 0.10582592, "tags": []}, {"Architecture": "x86_64", "BestOnDemandHourPriceRegion": "us-central1", "CanonicalName": "n4-highcpu-8", "InstanceType": "n4-highcpu-8", "Memory": 16384, "ModifiedDate": "2024-11-13 00:01:25", "PaymentType": "OnDemand", "PricePerHour": 0.139457232, "tags": []}, {"Architecture": "x86_64", "BestOnDemandHourPriceRegion": "us-central1", "CanonicalName": "e2-highmem-8", "InstanceType": "e2-highmem-8", "Memory": 65536, "ModifiedDate": "2024-11-13 00:01:25", "PaymentType": "OnDemand", "PricePerHour": 0.14276384, "tags": []}]}}