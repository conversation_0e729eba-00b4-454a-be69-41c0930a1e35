intent: postgres
flavor: alloydb
version: "0.1"
description: Adds Postgres module of flavor alloydb
clouds:
  - gcp
spec:
  title: AlloyDB
  type: object
  properties:
    postgres_version:
      type: string
      title: Postgres Version
      description: Specifies the Postgres version to use.
      enum:
        - "12.11"
        - "13.3"
        - "14.0"
      x-ui-error-message: "Please select a supported Postgres version."
    size:
      type: object
      title: Size
      description: Configuration for writer and reader instances.
      properties:
        writer:
          type: object
          title: Writer
          description: Writer instance settings.
          properties:
            cpu:
              type: integer
              title: CPU
              description: Number of CPU cores required.
              minimum: 1
              maximum: 96
              x-ui-error-message: "CPU must be between 1 and 96."
          required:
            - cpu
          x-ui-order:
            - cpu
        reader:
          type: object
          title: Reader
          description: Reader instance settings.
          properties:
            cpu:
              type: integer
              title: CPU
              description: Number of CPU cores required.
              minimum: 1
              maximum: 96
              x-ui-error-message: "CPU must be between 1 and 96."
            instance_count:
              type: integer
              title: Instance Count
              description: Number of reader instances to create.
              minimum: 0
              maximum: 10
              x-ui-error-message: "Instance count must be between 0 and 10."
          required:
            - cpu
            - instance_count
          x-ui-order:
            - cpu
            - instance_count
      required:
        - writer
      x-ui-order:
        - writer
        - reader
    db_names:
      type: array
      title: Additional Database Names
      description: List of additional databases to be created.
      pattern: "^(?!.*[_-]{2,})[a-zA-Z0-9]+([-_][a-zA-Z0-9]+)*(, [a-zA-Z0-9]+([-_][a-zA-Z0-9]+)*)*$|^(?!.*[_-]{2,})[a-zA-Z0-9]+([-_][a-zA-Z0-9]+)*(,[a-zA-Z0-9]+([-_][a-zA-Z0-9]+)*)*$|^(?!.*[_-]{2,})[a-zA-Z0-9]+([-_][a-zA-Z0-9]+)*(, [a-zA-Z0-9]+([-_][a-zA-Z0-9])+)*$"
      x-ui-error-message: "Database names must start with an alphanumeric character and should be separated by commas"
    db_schemas:
      type: object
      title: Additional Database Schemas
      description: Map of additonal schemas to be created.
      patternProperties:
        keyPattern: "^[a-zA-Z0-9-_]+$"
        title: Database Schemas
        description: Database schema row item which contains schema name and database name in which schema will be created.
        properties:
          db:
            title: Database Name
            type: string
            description: Name of the database under which schema will be created.
            pattern: "^[a-zA-Z0-9]+([-_][a-zA-Z0-9]+)*$"
            x-ui-error-message: Database name must start with an alphabet or underscore and should contain only alphanumeric characters and underscores
          schema:
            title: Database Schema
            type: string
            description: Name of the schema which needs to be created.
            pattern: "^[a-zA-Z0-9]+([-_][a-zA-Z0-9]+)*$"
        required:
          - db
          - schema
  required:
    - size
    - postgres_version
  x-ui-order:
    - postgres_version
    - size
    - db_names
    - db_schemas
sample:
  $schema: >-
    https://facets-cloud.github.io/facets-schemas/schemas/postgres/postgres.schema.json
  flavor: alloydb
  metadata: {}
  kind: postgres
  disabled: true
  version: "0.1"
  spec:
    postgres_version: "12.11"
    size:
      writer:
        cpu: 2
      reader:
        cpu: 2
        instance_count: 0
