{"kind": "grafana_dashboard", "flavor": "default", "version": "latest", "lifecycle": "ENVIRONMENT", "disabled": false, "provided": false, "depends_on": [], "metadata": {"name": "status-checks"}, "ui": {"base_resource": true}, "spec": {"dashboard": {"annotations": {"list": [{"builtIn": 1, "datasource": {"type": "datasource", "uid": "grafana"}, "enable": true, "hide": true, "iconColor": "rgba(0, 211, 255, 1)", "name": "Annotations & Alerts", "target": {"limit": 100, "matchAny": false, "tags": [], "type": "dashboard"}, "type": "dashboard"}]}, "editable": true, "fiscalYearStartMonth": 0, "graphTooltip": 0, "id": 24, "links": [], "liveNow": false, "panels": [{"datasource": {"type": "prometheus", "uid": "prometheus"}, "fieldConfig": {"defaults": {"color": {"mode": "thresholds"}, "custom": {"align": "auto", "displayMode": "color-background-solid", "filterable": true, "inspect": true, "minWidth": 50}, "mappings": [{"options": {"0": {"color": "dark-red", "index": 1, "text": "Unhealthy"}, "1": {"color": "dark-green", "index": 0, "text": "Healthy"}}, "type": "value"}], "thresholds": {"mode": "absolute", "steps": [{"color": "transparent", "value": null}]}}, "overrides": []}, "gridPos": {"h": 20, "w": 24, "x": 0, "y": 0}, "id": 12, "options": {"footer": {"enablePagination": true, "fields": "", "reducer": ["sum"], "show": false}, "showHeader": true, "sortBy": []}, "pluginVersion": "9.2.15", "targets": [{"datasource": {"type": "prometheus", "uid": "prometheus"}, "editorMode": "code", "exemplar": false, "expr": "facets_uptime_check\n", "format": "table", "instant": true, "legendFormat": "{{check}}", "range": false, "refId": "A"}], "title": "Health Checks Status", "transformations": [{"id": "filterFieldsByName", "options": {"include": {"names": ["Value", "check_name", "check_namespace", "check_type", "last_successful_runtime", "last_successful_run_duration", "message"]}}}], "type": "table"}], "refresh": "5s", "schemaVersion": 37, "style": "dark", "tags": [], "templating": {"list": []}, "time": {"from": "now-5m", "to": "now"}, "timepicker": {"refresh_intervals": ["5s", "10s", "30s", "1m", "5m", "15m", "30m", "1h", "2h", "1d"], "time_options": ["5m", "15m", "1h", "6h", "12h", "24h", "2d", "7d", "30d"]}, "timezone": "browser", "title": "Status Check Monitoring", "uid": "2u6ixlZdiL8ZI66x", "version": 1, "weekStart": ""}}, "out": {}, "advanced": {"inherit_from_base": true}}