intent: "cloud_account"
alias-flavors: ["default"]
flavor: gcp
lifecycle: ENVIRONMENT
version: "0.1"
depends_on: []
input_type: instance
composition: {}
conditional_on_intent: ""
clouds:
- gcp
spec: {}
outputs:
  default:
    type: "@outputs/cloud_account"
    providers:
      google-beta:
        source: hashicorp/google-beta
        version: 6.23.0
        attributes:
          project: attributes.project
          region: attributes.region
          credentials: attributes.credentials
      google:
        source: hashicorp/google
        version: 6.23.0
        attributes:
          project: attributes.project
          region: attributes.region
          credentials: attributes.credentials
      google5:
        source: hashicorp/google5
        version: 5.32.0
        attributes:
          project: attributes.project
          region: attributes.region
          credentials: attributes.credentials
sample:
  version: "0.1"
  flavor: gcp
  kind: cloud_account
  disabled: false
  spec: {}
