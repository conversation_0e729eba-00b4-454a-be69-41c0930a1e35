intent: mysql
flavor: rds
version: "0.1"
description: Adds MYSQL module of flavor rds
clouds:
  - aws
spec:
  title: RDS
  type: object
  properties:
    mysql_version:
      type: string
      title: MySQL Version
      description: Version of MySQL
      minLength: 1
      x-ui-placeholder: "Ex. 8.0"
      enum:
        - "8.0"
        - "5.7"
    size:
      type: object
      title: Size
      description: Writer and Reader Datastore Sizing
      properties:
        writer:
          type: object
          title: Writer
          description: Writer Node Configuration
          properties:
            instance:
              type: string
              title: Instance Type
              description: Type of Instance for Reader
              enum:
                - db.t4g.medium
                - db.t3.medium
                - db.t3.small
                - db.t3.micro
                - db.t3.large
                - db.t3.xlarge
                - db.t3.2xlarge
                - db.t2.micro
                - db.t2.small
                - db.t2.medium
                - db.t2.large
              x-ui-placeholder: "Select writer instance type"
          required:
            - instance
        reader:
          type: object
          title: Reader
          description: Reader Node Configuration
          properties:
            instance:
              type: string
              title: Instance Type
              description: Type of Instance for Reader
              enum:
                - db.t4g.medium
                - db.t3.medium
                - db.t3.small
                - db.t3.micro
                - db.t3.large
                - db.t3.xlarge
                - db.t3.2xlarge
                - db.t2.micro
                - db.t2.small
                - db.t2.medium
                - db.t2.large
              x-ui-placeholder: "Select reader instance type"
            instance_count:
              type: integer
              title: Instance Count
              description: Number of Reader Instances
              x-ui-placeholder: "Enter reader instance count"
              x-ui-error-message: "Instance count must be a non-negative integer"
              minimum: 0
              maximum: 20
          required:
            - instance
    apply_immediately:
      type: boolean
      title: Apply Immediately
      description: This specifies whether any modifications are applied immediately, or during the next maintenance window. Default is false
  required:
    - size
    - mysql_version
  x-ui-order:
    - mysql_version
    - size
sample:
  $schema: "https://facets-cloud.github.io/facets-schemas/schemas/mysql/mysql.schema.json"
  kind: mysql
  flavor: rds
  version: "0.1"
  disabled: true
  metadata:
    tags:
      managed-by: facets
  spec:
    mysql_version: "8.0"
    apply_immediately: false
    size:
      writer:
        instance: db.t4g.medium
      reader:
        instance: db.t4g.medium
        instance_count: 0
