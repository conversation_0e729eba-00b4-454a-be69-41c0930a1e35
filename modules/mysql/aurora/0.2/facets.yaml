intent: mysql
flavor: aurora
version: "0.2"
description: Adds MYSQL module of flavor aurora
clouds:
  - aws
spec:
  title: Aurora
  type: object
  properties:
    mysql_version:
      type: string
      title: MySQL Version
      description: Version of MySQL
      minLength: 1
      x-ui-placeholder: "Ex. 8.0"
    size:
      type: object
      title: Size
      description: Writer and Reader Datastore Sizing
      properties:
        reader:
          type: object
          title: Reader
          description: Reader Node Configuration
          properties:
            instance:
              type: string
              title: Instance Type
              description: Type of Instance for Reader
              enum:
                - db.t3.micro
                - db.t3.small
                - db.t3.medium
                - db.t3.large
                - db.t4g.micro
                - db.t4g.small
                - db.t4g.medium
                - db.t4g.large
                - db.m5.large
                - db.m5.xlarge
                - db.m5.x2large
                - db.m5.4xlarge
                - db.m5.8xlarge
                - db.m5.12xlarge
                - db.m6g.micro
                - db.m6g.small
                - db.m6g.medium
                - db.m6g.large
                - db.m6g.xlarge
                - db.m6g.x2large
                - db.m6g.4xlarge
                - db.m6g.8xlarge
                - db.m6g.12xlarge
                - db.r5.large
                - db.r5.xlarge
                - db.r5.2xlarge
                - db.r5.4xlarge
                - db.r5.8xlarge
                - db.r5.12xlarge
                - db.r6g.large
                - db.r6g.xlarge
                - db.r6g.2xlarge
                - db.r6g.4xlarge
                - db.r6g.8xlarge
                - db.r6g.12xlarge
              x-ui-placeholder: "Select reader instance type"
            instance_count:
              type: integer
              title: Instance Count
              description: Number of Reader Instances
              minimum: 0
              maximum: 20
              x-ui-placeholder: "Enter reader instance count"
              x-ui-error-message: "Instance count must be a non-negative integer"
          required:
            - instance
            - instance_count
        writer:
          type: object
          title: Writer
          description: Writer Node Configuration
          properties:
            instance:
              type: string
              title: Instance Type
              description: Type of Instance for Writer
              enum:
                - db.t3.micro
                - db.t3.small
                - db.t3.medium
                - db.t3.large
                - db.t4g.micro
                - db.t4g.small
                - db.t4g.medium
                - db.t4g.large
                - db.m5.large
                - db.m5.xlarge
                - db.m5.x2large
                - db.m5.4xlarge
                - db.m5.8xlarge
                - db.m5.12xlarge
                - db.m6g.micro
                - db.m6g.small
                - db.m6g.medium
                - db.m6g.large
                - db.m6g.xlarge
                - db.m6g.x2large
                - db.m6g.4xlarge
                - db.m6g.8xlarge
                - db.m6g.12xlarge
                - db.r5.large
                - db.r5.xlarge
                - db.r5.2xlarge
                - db.r5.4xlarge
                - db.r5.8xlarge
                - db.r5.12xlarge
                - db.r6g.large
                - db.r6g.xlarge
                - db.r6g.2xlarge
                - db.r6g.4xlarge
                - db.r6g.8xlarge
                - db.r6g.12xlarge
              x-ui-placeholder: "Select writer instance type"
            instance_count:
              type: integer
              title: Instance Count
              description: Number of Writer Instances
              minimum: 1
              maximum: 20
              x-ui-placeholder: "Enter writer instance count"
          required:
            - instance
    apply_immediately:
      type: boolean
      title: Apply Immediately
      description: This specifies whether any modifications are applied immediately, or during the next maintenance window. Default is false
  required:
    - size
    - mysql_version
  x-ui-order:
    - mysql_version
    - size
sample:
  $schema: "https://facets-cloud.github.io/facets-schemas/schemas/mysql/mysql.schema.json"
  kind: mysql
  flavor: aurora
  version: "0.2"
  disabled: true
  metadata:
    tags:
      managed-by: facets
  spec:
    mysql_version: 8.0.mysql_aurora.3.05.2
    apply_immediately: false
    size:
      writer:
        instance: db.t4g.medium
        instance_count: 1
      reader:
        instance_count: 0
        instance: db.t4g.medium
