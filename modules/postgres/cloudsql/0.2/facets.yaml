intent: postgres
flavor: cloudsql
version: "0.2"
description: Adds Postgres module of flavor cloudsql
clouds:
  - gcp
spec:
  title: CloudSQL
  type: object
  properties:
    postgres_version:
      type: string
      title: Postgres Version
      description: Specifies the Postgres version to use.
      enum:
        - "12.11"
        - "13.3"
        - "14.0"
      x-ui-error-message: "Please select a supported Postgres version."
    size:
      type: object
      title: Size
      description: Configuration for writer and reader instances.
      properties:
        writer:
          type: object
          title: Writer
          description: Writer instance settings.
          properties:
            instance:
              type: string
              title: Instance Type
              description: The instance type of the node.
              minLength: 1
              x-ui-placeholder: "e.g., 'db-f1-micro'"
              enum:
                - db-f1-micro
                - db-g1-small
                - db-n1-standard-1
                - db-n1-standard-2
                - db-n1-standard-4
                - db-n1-standard-8
                - db-n1-standard-16
                - db-n1-standard-32
                - db-n1-highmem-2
                - db-n1-highmem-4
                - db-n1-highmem-8
                - db-n1-highmem-16
                - db-n1-highmem-32
                - db-m2-ultramem-208
                - db-m2-ultramem-416
                - db-custom-1-3840
                - db-custom-2-7680
                - db-custom-4-15360
                - db-custom-8-30720
                - db-custom-16-61440
                - db-custom-32-122880
              x-ui-error-message: "Please select a valid instance type."
            volume:
              type: number
              title: Volume
              description: The size of the volume in GB.
              minimum: 10
              x-ui-placeholder: "e.g., 10"
              x-ui-error-message: "Please specify the volume size as a number in GB. The minimum is 10GB."
          required:
            - instance
            - volume
          x-ui-order:
            - instance
            - volume
        reader:
          type: object
          title: Reader
          description: Reader instance settings.
          properties:
            instance:
              type: string
              title: Instance Type
              description: The instance type of the node.
              minLength: 1
              x-ui-placeholder: "e.g., 'db-g1-small'"
              enum:
                - db-f1-micro
                - db-g1-small
                - db-n1-standard-1
                - db-n1-standard-2
                - db-n1-standard-4
                - db-n1-standard-8
                - db-n1-standard-16
                - db-n1-standard-32
                - db-n1-highmem-2
                - db-n1-highmem-4
                - db-n1-highmem-8
                - db-n1-highmem-16
                - db-n1-highmem-32
                - db-m2-ultramem-208
                - db-m2-ultramem-416
                - db-custom-1-3840
                - db-custom-2-7680
                - db-custom-4-15360
                - db-custom-8-30720
                - db-custom-16-61440
                - db-custom-32-122880
              x-ui-error-message: "Please select a valid instance type."
            volume:
              type: number
              title: Volume
              description: The size of the volume in GB.
              minimum: 10
              x-ui-placeholder: "e.g., 10"
              x-ui-error-message: "Please specify the volume size as a number in GB. The minimum is 10GB."
            instance_count:
              type: integer
              title: Instance Count
              description: Number of instances to create.
              minimum: 0
              maximum: 16
              x-ui-placeholder: "e.g., 2"
              x-ui-error-message: "Instance count must be between 0 and 16."
          required:
            - instance
            - volume
            - instance_count
          x-ui-order:
            - instance
            - volume
            - instance_count
      required:
        - writer
      x-ui-order:
        - writer
        - reader
    db_names:
      type: array
      title: Additional Database Names
      description: List of additional databases to be created
      pattern: "^(?!.*[_-]{2,})[a-zA-Z0-9]+([-_][a-zA-Z0-9]+)*(, [a-zA-Z0-9]+([-_][a-zA-Z0-9]+)*)*$|^(?!.*[_-]{2,})[a-zA-Z0-9]+([-_][a-zA-Z0-9]+)*(,[a-zA-Z0-9]+([-_][a-zA-Z0-9]+)*)*$|^(?!.*[_-]{2,})[a-zA-Z0-9]+([-_][a-zA-Z0-9]+)*(, [a-zA-Z0-9]+([-_][a-zA-Z0-9])+)*$"
      x-ui-error-message: "Database names must start with an alphanumeric character and should be separated by commas"
    db_schemas:
      type: object
      title: Additional Database Schemas
      description: Map of additonal schemas to be created.
      patternProperties:
        keyPattern: "^[a-zA-Z0-9-_]+$"
        title: Database Schemas
        description: Database schema row item which contains schema name and database name in which schema will be created.
        properties:
          db:
            title: Database Name
            type: string
            description: Name of the database under which schema will be created.
            pattern: "^[a-zA-Z0-9]+([-_][a-zA-Z0-9]+)*$"
            x-ui-error-message: Database name must start with an alphabet or underscore and should contain only alphanumeric characters and underscores
          schema:
            title: Database Schema
            type: string
            description: Name of the schema which needs to be created.
            pattern: "^[a-zA-Z0-9]+([-_][a-zA-Z0-9]+)*$"
        required:
          - db
          - schema
  required:
    - size
    - postgres_version
  x-ui-order:
    - postgres_version
    - size
    - db_names
    - db_schemas
sample:
  $schema: >-
    https://facets-cloud.github.io/facets-schemas/schemas/postgres/postgres.schema.json
  flavor: cloudsql
  disabled: true
  version: "0.2"
  kind: postgres
  metadata: {}
  spec:
    size:
      writer:
        instance: db-f1-micro
        volume: 10
      reader:
        instance: db-f1-micro
        volume: 10
        instance_count: 0
    postgres_version: "12.11"
