intent: mysql
flavor: flexible_server
version: "0.1"
description: Adds MYSQL module of flavor flexible server
clouds:
  - azure
spec:
  title: Flexible Server
  type: object
  properties:
    mysql_version:
      type: string
      title: MySQL Version
      description: Version of MySQL
      minLength: 1
      x-ui-placeholder: "Ex. 8.0.21"
      enum:
        - "5.7"
        - "8.0.21"
    size:
      type: object
      title: Size
      description: Writer and Reader Datastore Sizing
      properties:
        reader:
          type: object
          title: Reader
          description: Reader Node Configuration
          properties:
            instance:
              type: string
              title: Instance Type
              description: Type of Instance for Reader
              enum:
                - GP_Standard_D2ds_v4
                - GP_Standard_D4ds_v4
                - GP_Standard_D8ds_v4
                - GP_Standard_D16ds_v4
                - GP_Standard_D32ds_v4
                - GP_Standard_D48ds_v4
                - GP_Standard_D64ds_v4
                - GP_Standard_E2ds_v4
                - GP_Standard_E4ds_v4
                - GP_Standard_E8ds_v4
                - GP_Standard_E16ds_v4
                - GP_Standard_E20ds_v4
                - GP_Standard_E32ds_v4
                - GP_Standard_E48ds_v4
                - GP_Standard_E64ds_v4
                - GP_Standard_E80ds_v4
                - Standard_B1s
                - Standard_B1ms
                - Standard_B2s
                - Standard_B2ms
                - Standard_B4ms
                - Standard_B8ms
              x-ui-placeholder: "Select reader instance type"
            instance_count:
              type: integer
              title: Instance Count
              description: Number of reader instances
              minimum: 0
              maximum: 20
              x-ui-placeholder: "Enter reader instance count"
              x-ui-error-message: "Instance count must be a non-negative integer"
          required:
            - instance
            - instance_count
        writer:
          type: object
          title: Writer
          description: Writer Node Configuration
          properties:
            instance:
              type: string
              title: Instance Type
              description: Type of Instance for Reader
              enum:
                - GP_Standard_D2ds_v4
                - GP_Standard_D4ds_v4
                - GP_Standard_D8ds_v4
                - GP_Standard_D16ds_v4
                - GP_Standard_D32ds_v4
                - GP_Standard_D48ds_v4
                - GP_Standard_D64ds_v4
                - GP_Standard_E2ds_v4
                - GP_Standard_E4ds_v4
                - GP_Standard_E8ds_v4
                - GP_Standard_E16ds_v4
                - GP_Standard_E20ds_v4
                - GP_Standard_E32ds_v4
                - GP_Standard_E48ds_v4
                - GP_Standard_E64ds_v4
                - GP_Standard_E80ds_v4
                - Standard_B1s
                - Standard_B1ms
                - Standard_B2s
                - Standard_B2ms
                - Standard_B4ms
                - Standard_B8ms
              x-ui-placeholder: "Select writer instance type"
          required:
            - instance
  required:
    - size
    - mysql_version
  x-ui-order:
    - mysql_version
    - size
sample:
  flavor: flexible_server
  $schema: "https://facets-cloud.github.io/facets-schemas/schemas/mysql/mysql.schema.json"
  metadata:
    tags:
      managed-by: facets
  kind: mysql
  disabled: true
  version: "0.1"
  spec:
    mysql_version: 8.0.21
    size:
      writer:
        instance: GP_Standard_D2ds_v4
      reader:
        instance: GP_Standard_D2ds_v4
        instance_count: 0
