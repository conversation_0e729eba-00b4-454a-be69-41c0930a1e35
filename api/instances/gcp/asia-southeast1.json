{"16Cores": {"onDemand": [{"Architecture": "x86_64", "BestOnDemandHourPriceRegion": "us-central1", "CanonicalName": "e2-highcpu-16", "InstanceType": "e2-highcpu-16", "Memory": 16384, "ModifiedDate": "2024-11-13 00:01:25", "PaymentType": "OnDemand", "PricePerHour": 0.488244, "tags": ["cheapest"]}, {"Architecture": "x86_64", "BestOnDemandHourPriceRegion": "asia-south1", "CanonicalName": "n2d-highcpu-16", "InstanceType": "n2d-highcpu-16", "Memory": 16384, "ModifiedDate": "2024-11-13 00:01:25", "PaymentType": "OnDemand", "PricePerHour": 0.615616, "tags": []}, {"Architecture": "x86_64", "BestOnDemandHourPriceRegion": "us-central1", "CanonicalName": "e2-standard-16", "InstanceType": "e2-standard-16", "Memory": 65536, "ModifiedDate": "2024-11-13 00:01:25", "PaymentType": "OnDemand", "PricePerHour": 0.66132912, "tags": []}, {"Architecture": "x86_64", "BestOnDemandHourPriceRegion": "us-central1", "CanonicalName": "n1-highcpu-16", "InstanceType": "n1-highcpu-16", "Memory": 14746, "ModifiedDate": "2024-11-13 00:01:25", "PaymentType": "OnDemand", "PricePerHour": 0.69924044140625, "tags": []}], "spot": [{"Architecture": "x86_64", "BestOnDemandHourPriceRegion": "asia-south1", "CanonicalName": "n2d-highcpu-16", "InstanceType": "n2d-highcpu-16", "Memory": 16384, "ModifiedDate": "2024-11-13 00:01:25", "PaymentType": "OnDemand", "PricePerHour": 0.109568, "tags": ["cheapest"]}, {"Architecture": "x86_64", "BestOnDemandHourPriceRegion": "us-central1", "CanonicalName": "n1-highcpu-16", "InstanceType": "n1-highcpu-16", "Memory": 14746, "ModifiedDate": "2024-11-13 00:01:25", "PaymentType": "OnDemand", "PricePerHour": 0.119768349609375, "tags": []}, {"Architecture": "x86_64", "BestOnDemandHourPriceRegion": "asia-south1", "CanonicalName": "c2d-highcpu-16", "InstanceType": "c2d-highcpu-16", "Memory": 32768, "ModifiedDate": "2024-11-13 00:01:25", "PaymentType": "OnDemand", "PricePerHour": 0.13561600000000001, "tags": []}, {"Architecture": "x86_64", "BestOnDemandHourPriceRegion": "asia-south1", "CanonicalName": "n2d-standard-16", "InstanceType": "n2d-standard-16", "Memory": 65536, "ModifiedDate": "2024-11-13 00:01:25", "PaymentType": "OnDemand", "PricePerHour": 0.148352, "tags": []}]}, "4Cores": {"onDemand": [{"Architecture": "x86_64", "BestOnDemandHourPriceRegion": "us-central1", "CanonicalName": "e2-highcpu-4", "InstanceType": "e2-highcpu-4", "Memory": 4096, "ModifiedDate": "2024-11-13 00:01:25", "PaymentType": "OnDemand", "PricePerHour": 0.122061, "tags": ["cheapest"]}, {"Architecture": "x86_64", "BestOnDemandHourPriceRegion": "asia-south1", "CanonicalName": "n2d-highcpu-4", "InstanceType": "n2d-highcpu-4", "Memory": 4096, "ModifiedDate": "2024-11-13 00:01:25", "PaymentType": "OnDemand", "PricePerHour": 0.153904, "tags": []}, {"Architecture": "x86_64", "BestOnDemandHourPriceRegion": "us-central1", "CanonicalName": "e2-standard-4", "InstanceType": "e2-standard-4", "Memory": 16384, "ModifiedDate": "2024-11-13 00:01:25", "PaymentType": "OnDemand", "PricePerHour": 0.16533228, "tags": []}, {"Architecture": "x86_64", "BestOnDemandHourPriceRegion": "us-central1", "CanonicalName": "n1-highcpu-4", "InstanceType": "n1-highcpu-4", "Memory": 3686, "ModifiedDate": "2024-11-13 00:01:25", "PaymentType": "OnDemand", "PricePerHour": 0.17480755859375, "tags": []}], "spot": [{"Architecture": "x86_64", "BestOnDemandHourPriceRegion": "asia-south1", "CanonicalName": "n2d-highcpu-4", "InstanceType": "n2d-highcpu-4", "Memory": 4096, "ModifiedDate": "2024-11-13 00:01:25", "PaymentType": "OnDemand", "PricePerHour": 0.027392, "tags": ["cheapest"]}, {"Architecture": "x86_64", "BestOnDemandHourPriceRegion": "us-central1", "CanonicalName": "n1-highcpu-4", "InstanceType": "n1-highcpu-4", "Memory": 3686, "ModifiedDate": "2024-11-13 00:01:25", "PaymentType": "OnDemand", "PricePerHour": 0.029941650390625, "tags": []}, {"Architecture": "x86_64", "BestOnDemandHourPriceRegion": "asia-south1", "CanonicalName": "c2d-highcpu-4", "InstanceType": "c2d-highcpu-4", "Memory": 8192, "ModifiedDate": "2024-11-13 00:01:25", "PaymentType": "OnDemand", "PricePerHour": 0.033904000000000004, "tags": []}, {"Architecture": "x86_64", "BestOnDemandHourPriceRegion": "asia-south1", "CanonicalName": "n2d-standard-4", "InstanceType": "n2d-standard-4", "Memory": 16384, "ModifiedDate": "2024-11-13 00:01:25", "PaymentType": "OnDemand", "PricePerHour": 0.037088, "tags": []}]}, "8Cores": {"onDemand": [{"Architecture": "x86_64", "BestOnDemandHourPriceRegion": "us-central1", "CanonicalName": "e2-highcpu-8", "InstanceType": "e2-highcpu-8", "Memory": 8192, "ModifiedDate": "2024-11-13 00:01:25", "PaymentType": "OnDemand", "PricePerHour": 0.244122, "tags": ["cheapest"]}, {"Architecture": "x86_64", "BestOnDemandHourPriceRegion": "asia-south1", "CanonicalName": "n2d-highcpu-8", "InstanceType": "n2d-highcpu-8", "Memory": 8192, "ModifiedDate": "2024-11-13 00:01:25", "PaymentType": "OnDemand", "PricePerHour": 0.307808, "tags": []}, {"Architecture": "x86_64", "BestOnDemandHourPriceRegion": "us-central1", "CanonicalName": "e2-standard-8", "InstanceType": "e2-standard-8", "Memory": 32768, "ModifiedDate": "2024-11-13 00:01:25", "PaymentType": "OnDemand", "PricePerHour": 0.33066456, "tags": []}, {"Architecture": "x86_64", "BestOnDemandHourPriceRegion": "us-central1", "CanonicalName": "n1-highcpu-8", "InstanceType": "n1-highcpu-8", "Memory": 7373, "ModifiedDate": "2024-11-13 00:01:25", "PaymentType": "OnDemand", "PricePerHour": 0.349620220703125, "tags": []}], "spot": [{"Architecture": "x86_64", "BestOnDemandHourPriceRegion": "asia-south1", "CanonicalName": "n2d-highcpu-8", "InstanceType": "n2d-highcpu-8", "Memory": 8192, "ModifiedDate": "2024-11-13 00:01:25", "PaymentType": "OnDemand", "PricePerHour": 0.054784, "tags": ["cheapest"]}, {"Architecture": "x86_64", "BestOnDemandHourPriceRegion": "us-central1", "CanonicalName": "n1-highcpu-8", "InstanceType": "n1-highcpu-8", "Memory": 7373, "ModifiedDate": "2024-11-13 00:01:25", "PaymentType": "OnDemand", "PricePerHour": 0.0598841748046875, "tags": []}, {"Architecture": "x86_64", "BestOnDemandHourPriceRegion": "asia-south1", "CanonicalName": "c2d-highcpu-8", "InstanceType": "c2d-highcpu-8", "Memory": 16384, "ModifiedDate": "2024-11-13 00:01:25", "PaymentType": "OnDemand", "PricePerHour": 0.06780800000000001, "tags": []}, {"Architecture": "x86_64", "BestOnDemandHourPriceRegion": "asia-south1", "CanonicalName": "n2d-standard-8", "InstanceType": "n2d-standard-8", "Memory": 32768, "ModifiedDate": "2024-11-13 00:01:25", "PaymentType": "OnDemand", "PricePerHour": 0.074176, "tags": []}]}}