{"kind": "grafana_dashboard", "flavor": "default", "version": "latest", "lifecycle": "ENVIRONMENT", "disabled": false, "provided": false, "depends_on": [], "metadata": {"name": "ingress-overview"}, "ui": {"base_resource": true}, "spec": {"dashboard": {"annotations": {"list": [{"builtIn": 1, "datasource": {"type": "grafana", "uid": "-- <PERSON><PERSON> --"}, "enable": true, "hide": true, "iconColor": "rgba(0, 211, 255, 1)", "name": "Annotations & Alerts", "target": {"limit": 100, "matchAny": false, "tags": [], "type": "dashboard"}, "type": "dashboard"}]}, "editable": true, "fiscalYearStartMonth": 0, "graphTooltip": 0, "id": 27, "links": [], "liveNow": false, "panels": [{"datasource": {"type": "prometheus", "uid": "prometheus"}, "description": "The total number of requests per second for services", "fieldConfig": {"defaults": {"color": {"fixedColor": "blue", "mode": "thresholds"}, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": null}]}, "unit": "reqps"}, "overrides": []}, "gridPos": {"h": 8, "w": 12, "x": 0, "y": 0}, "id": 2, "options": {"orientation": "auto", "reduceOptions": {"calcs": [], "fields": "", "values": false}, "showThresholdLabels": false, "showThresholdMarkers": true}, "pluginVersion": "9.2.15", "targets": [{"datasource": {"type": "prometheus", "uid": "prometheus"}, "editorMode": "code", "expr": "round(sum(irate(nginx_ingress_controller_requests{namespace=\"$namespace\", exported_service=~\"$services\"}[2m])), 0.001) or round(sum(irate(nginx_ingress_controller_requests{namespace=\"$namespace\", service=~\"$services\"}[2m])), 0.001)", "legendFormat": "{{ ingress }}", "range": true, "refId": "A"}], "title": "Total Request Volume", "type": "gauge"}, {"datasource": {"type": "prometheus", "uid": "prometheus"}, "description": "The percentile response times (P99, P90, P50) for each ingress for services", "fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}, "custom": {"axisCenteredZero": false, "axisColorMode": "text", "axisLabel": "", "axisPlacement": "auto", "barAlignment": 0, "drawStyle": "line", "fillOpacity": 0, "gradientMode": "none", "hideFrom": {"legend": false, "tooltip": false, "viz": false}, "lineInterpolation": "linear", "lineWidth": 1, "pointSize": 5, "scaleDistribution": {"type": "linear"}, "showPoints": "auto", "spanNulls": false, "stacking": {"group": "A", "mode": "none"}, "thresholdsStyle": {"mode": "off"}}, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": null}, {"color": "red", "value": 80}]}, "unit": "s"}, "overrides": []}, "gridPos": {"h": 8, "w": 12, "x": 12, "y": 0}, "id": 4, "options": {"legend": {"calcs": [], "displayMode": "list", "placement": "bottom", "showLegend": true}, "tooltip": {"mode": "single", "sort": "none"}}, "targets": [{"datasource": {"type": "prometheus", "uid": "prometheus"}, "editorMode": "code", "exemplar": false, "expr": "histogram_quantile(0.50, sum(rate(nginx_ingress_controller_request_duration_seconds_bucket{namespace=\"$namespace\",ingress=~\"$ingress\"}[2m])) by (le))", "instant": false, "interval": "", "legendFormat": "P50 {{ ingress }}", "range": true, "refId": "A"}, {"datasource": {"type": "prometheus", "uid": "prometheus"}, "editorMode": "code", "expr": "histogram_quantile(0.90, sum(rate(nginx_ingress_controller_request_duration_seconds_bucket{namespace=\"$namespace\", ingress=~\"$ingress\"}[2m])) by (le))", "hide": false, "legendFormat": "P90 {{ ingress }}", "range": true, "refId": "B"}, {"datasource": {"type": "prometheus", "uid": "prometheus"}, "editorMode": "code", "expr": "histogram_quantile(0.99, sum(rate(nginx_ingress_controller_request_duration_seconds_bucket{namespace=\"$namespace\", ingress=~\"$ingress\"}[2m])) by (le))", "hide": false, "legendFormat": "P99 {{ ingress }}", "range": true, "refId": "C"}], "title": "Percentile Response Times", "type": "timeseries"}, {"datasource": {"type": "prometheus", "uid": "prometheus"}, "description": "The status code count for each ingress for the services", "fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}, "custom": {"axisCenteredZero": false, "axisColorMode": "text", "axisLabel": "", "axisPlacement": "auto", "barAlignment": 0, "drawStyle": "line", "fillOpacity": 0, "gradientMode": "none", "hideFrom": {"legend": false, "tooltip": false, "viz": false}, "lineInterpolation": "linear", "lineWidth": 1, "pointSize": 5, "scaleDistribution": {"type": "linear"}, "showPoints": "auto", "spanNulls": false, "stacking": {"group": "A", "mode": "none"}, "thresholdsStyle": {"mode": "off"}}, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": null}]}, "unit": "none"}, "overrides": []}, "gridPos": {"h": 8, "w": 12, "x": 0, "y": 8}, "id": 8, "options": {"legend": {"calcs": [], "displayMode": "list", "placement": "bottom", "showLegend": true}, "tooltip": {"mode": "single", "sort": "none"}}, "targets": [{"datasource": {"type": "prometheus", "uid": "prometheus"}, "editorMode": "code", "expr": "sum(label_replace(increase(nginx_ingress_controller_requests{namespace=\"$namespace\",exported_service=~\"$services\"}[2m]), \"status_code\", \"${1}xx\", \"status\", \"(.)..\")) or sum(label_replace(increase(nginx_ingress_controller_requests{namespace=\"$namespace\",service=~\"$services\"}[2m]), \"status_code\", \"${1}xx\", \"status\", \"(.)..\")) by (status_code)", "hide": false, "legendFormat": "{{status_code}}", "range": true, "refId": "B"}], "title": "Status Code Count", "type": "timeseries"}, {"datasource": {"type": "prometheus", "uid": "prometheus"}, "description": "The health of the services", "fieldConfig": {"defaults": {"color": {"mode": "thresholds"}, "custom": {"align": "center", "displayMode": "color-background-solid", "inspect": false}, "mappings": [{"options": {"0": {"color": "green", "index": 0, "text": "HEALTHY"}}, "type": "value"}, {"options": {"from": 1, "result": {"color": "red", "index": 1, "text": "UNHEALTHY"}, "to": **********}, "type": "range"}], "thresholds": {"mode": "absolute", "steps": [{"color": "transparent", "value": null}]}}, "overrides": []}, "gridPos": {"h": 8, "w": 12, "x": 12, "y": 8}, "id": 11, "options": {"footer": {"enablePagination": false, "fields": "", "reducer": ["sum"], "show": false}, "showHeader": true, "sortBy": [{"desc": true, "displayName": "container"}]}, "pluginVersion": "9.2.15", "targets": [{"datasource": {"type": "prometheus", "uid": "prometheus"}, "editorMode": "code", "exemplar": false, "expr": "kube_deployment_status_replicas_unavailable{namespace=\"$namespace\", deployment=~\"$services\"}", "format": "table", "instant": true, "legendFormat": "{{deployment}}", "range": false, "refId": "A"}], "title": "Services Health", "transformations": [{"id": "filterFieldsByName", "options": {"include": {"names": ["deployment", "Value"]}}}], "type": "table"}, {"datasource": {"type": "prometheus", "uid": "prometheus"}, "description": "The SSL expiry for hosts in each ingress for the services", "fieldConfig": {"defaults": {"color": {"mode": "thresholds"}, "custom": {"align": "auto", "displayMode": "color-background-solid", "filterable": false, "inspect": false}, "mappings": [{"options": {"from": 0, "result": {"color": "red", "index": 0}, "to": 7}, "type": "range"}, {"options": {"from": 8, "result": {"color": "yellow", "index": 1}, "to": 14}, "type": "range"}, {"options": {"from": -**********0, "result": {"color": "red", "index": 2}, "to": 0}, "type": "range"}], "thresholds": {"mode": "absolute", "steps": [{"color": "transparent", "value": null}]}, "unit": "days"}, "overrides": [{"matcher": {"id": "by<PERSON><PERSON>", "options": "common_name"}, "properties": [{"id": "custom.width", "value": 553}]}, {"matcher": {"id": "by<PERSON><PERSON>", "options": "domain"}, "properties": [{"id": "custom.width", "value": 402}]}]}, "gridPos": {"h": 10, "w": 24, "x": 0, "y": 16}, "id": 13, "options": {"footer": {"fields": "", "reducer": ["sum"], "show": false}, "showHeader": true, "sortBy": []}, "pluginVersion": "9.2.15", "targets": [{"datasource": {"type": "prometheus", "uid": "prometheus"}, "editorMode": "code", "exemplar": false, "expr": "ssl_expiry{namespace=\"$namespace\", ingress=~\"$ingress\", domain=~\"$host\"}", "format": "table", "hide": false, "instant": true, "legendFormat": "__auto", "range": false, "refId": "A"}], "title": "SSL Expiry", "transformations": [{"id": "filterFieldsByName", "options": {"include": {"names": ["domain", "Value", "common_name", "ingress"]}}}, {"id": "sortBy", "options": {"fields": {}, "sort": [{"field": "Value"}]}}, {"id": "organize", "options": {"excludeByName": {}, "indexByName": {"Value": 3, "common_name": 1, "domain": 2, "ingress": 0}, "renameByName": {}}}], "type": "table"}], "refresh": "5s", "schemaVersion": 37, "style": "dark", "tags": [], "templating": {"list": [{"current": {"selected": true, "text": "default", "value": "default"}, "datasource": {"type": "prometheus", "uid": "prometheus"}, "definition": "label_values(kube_ingress_path, namespace)", "description": "The list of namespace for the ingress backend", "hide": 0, "includeAll": false, "label": "Namespace", "multi": false, "name": "namespace", "options": [], "query": {"query": "label_values(kube_ingress_path, namespace)", "refId": "StandardVariableQuery"}, "refresh": 1, "regex": "", "skipUrlSync": false, "sort": 0, "type": "query"}, {"allValue": "", "current": {"selected": true, "text": ["All"], "value": ["$__all"]}, "datasource": {"type": "prometheus", "uid": "prometheus"}, "definition": "label_values(kube_ingress_path{namespace=~\"$namespace\"}, service_name)", "description": "The list of backends from ingresses", "hide": 0, "includeAll": true, "label": "Services", "multi": true, "name": "services", "options": [], "query": {"query": "label_values(kube_ingress_path{namespace=~\"$namespace\"}, service_name)", "refId": "StandardVariableQuery"}, "refresh": 1, "regex": "", "skipUrlSync": false, "sort": 0, "type": "query"}, {"allValue": "", "current": {"selected": true, "text": ["All"], "value": ["$__all"]}, "datasource": {"type": "prometheus", "uid": "prometheus"}, "definition": "label_values(kube_ingress_path{namespace=~\"$namespace\",  service_name=~\"$services\"}, ingress)", "hide": 2, "includeAll": true, "label": "Ingress", "multi": true, "name": "ingress", "options": [], "query": {"query": "label_values(kube_ingress_path{namespace=~\"$namespace\",  service_name=~\"$services\"}, ingress)", "refId": "StandardVariableQuery"}, "refresh": 2, "regex": "", "skipUrlSync": false, "sort": 1, "type": "query"}, {"allValue": "", "current": {"selected": true, "text": ["All"], "value": ["$__all"]}, "datasource": {"type": "prometheus", "uid": "prometheus"}, "definition": "label_values(kube_ingress_path{namespace=~\"$namespace\",  service_name=~\"$services\"}, host)", "hide": 2, "includeAll": true, "label": "host", "multi": true, "name": "host", "options": [], "query": {"query": "label_values(kube_ingress_path{namespace=~\"$namespace\",  service_name=~\"$services\"}, host)", "refId": "StandardVariableQuery"}, "refresh": 2, "regex": "", "skipUrlSync": false, "sort": 1, "type": "query"}]}, "time": {"from": "now-5m", "to": "now"}, "timepicker": {}, "timezone": "", "title": "Ingress Overview", "uid": "12Y89zt57LUwgq5l", "version": 1, "weekStart": ""}}, "out": {}, "advanced": {"inherit_from_base": true, "default": {"preserve_uid": true}}}