intent: crossplane
flavor: default
version: '0.1'
description: Adds crossplane - default flavor
clouds:
- aws
- gcp
- azure
- kubernetes
lifecycle: ENVIRONMENT
input_type: instance
composition: {}
inputs:
  kubernetes_details:
    type: "@output/kubernetes"
    displayName: Kubernetes Cluster
    description: The details of kubernetes cluster where the crossplane will be installed
    optional: false
    default:
      resource_type: kubernetes_cluster
      resource_name: default
spec:
  title: Crossplane Spec
  description: Specifications for Crossplane
  type: object
  properties:
    size:
      type: object
      title: Size
      description: Sizing for Crossplane
      properties:
        cpu_limit:
          type: string
          title: CPU Limit
          description: CPU limit for the instance
          minLength: 1
          pattern: "^((1-9|1[0-9]|2[0-9]|3[0-2])|((100|[1-9][0-9]{2,3}|[1-2][0-9]{4}|3[0-1][0-9]{3}|32000)m))$"
          x-ui-placeholder: "Enter CPU requests for your application"
          x-ui-error-message: "Value doesn't match pattern, it should be number ranging from 1 to 32 or 100m to 32000m"
        memory_limit:
          type: string
          title: Memory Limit
          description: Memory limit for the instance
          minLength: 1
          pattern: "^(([1-9]|[1-5][0-9]|6[0-4])(\\.[0-9]+)?Gi$)|(^(256|[2-9][0-9]{2,3}|[1-5][0-9]{4}|6[0-3][0-9]{3}|64000)(\\.[0-9]+)?Mi$)"
          x-ui-placeholder: "Enter Memory requests for your application"
          x-ui-error-message: "Value doesn't match pattern, it should be number ranging from 1Gi to 64Gi or 256Mi to 64000Mi"
    values:
      type: object
      title: Values
      description: The values to be passed on to the chart in the form of a YAML object
      x-ui-yaml-editor: true
      x-ui-placeholder: Enter values in YAML format
sample:
  kind: crossplane
  flavor: default
  version: '0.1'
  metadata: {}
  disabled: true
  spec:
    size:
      cpu_limit: 100m
      memory_limit: 256Mi
    values: {}
